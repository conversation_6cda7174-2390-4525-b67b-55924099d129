//+------------------------------------------------------------------+
//|                                                   Supertrend.mq5 |
//|                                  Copyright 2024, Supertrend EA  |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Supertrend EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Supertrend Indicator - Converted from Pine Script"

#property indicator_chart_window
#property indicator_buffers 4
#property indicator_plots   2

//--- Plot settings
#property indicator_label1  "Supertrend Up"
#property indicator_type1   DRAW_LINE
#property indicator_color1  clrGreen
#property indicator_style1  STYLE_SOLID
#property indicator_width1  2

#property indicator_label2  "Supertrend Down"
#property indicator_type2   DRAW_LINE
#property indicator_color2  clrRed
#property indicator_style2  STYLE_SOLID
#property indicator_width2  2

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input int                ATR_Period = 10;                    // ATR Period
input double             ATR_Multiplier = 3.0;              // ATR Multiplier
input bool               Use_Smoothed_ATR = true;           // Use Smoothed ATR (true=ATR, false=SMA)
input ENUM_APPLIED_PRICE Source_Price = PRICE_MEDIAN;       // Source Price (HL2)
input bool               Show_Signals = true;               // Show Buy/Sell Signals
input bool               Highlight_Zones = true;            // Highlight Trend Zones

//+------------------------------------------------------------------+
//| Indicator Buffers                                               |
//+------------------------------------------------------------------+
double SupertrendUpBuffer[];
double SupertrendDownBuffer[];
double TrendBuffer[];
double ATRBuffer[];

//--- Global variables
int atr_handle;
int prev_trend = 1;

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set indicator buffers
    SetIndexBuffer(0, SupertrendUpBuffer, INDICATOR_DATA);
    SetIndexBuffer(1, SupertrendDownBuffer, INDICATOR_DATA);
    SetIndexBuffer(2, TrendBuffer, INDICATOR_CALCULATIONS);
    SetIndexBuffer(3, ATRBuffer, INDICATOR_CALCULATIONS);
    
    //--- Set buffer properties
    PlotIndexSetInteger(0, PLOT_DRAW_TYPE, DRAW_LINE);
    PlotIndexSetInteger(1, PLOT_DRAW_TYPE, DRAW_LINE);
    
    //--- Set empty values
    PlotIndexSetDouble(0, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    PlotIndexSetDouble(1, PLOT_EMPTY_VALUE, EMPTY_VALUE);
    
    //--- Set arrays as series
    ArraySetAsSeries(SupertrendUpBuffer, true);
    ArraySetAsSeries(SupertrendDownBuffer, true);
    ArraySetAsSeries(TrendBuffer, true);
    ArraySetAsSeries(ATRBuffer, true);
    
    //--- Create ATR indicator
    if(Use_Smoothed_ATR)
        atr_handle = iATR(Symbol(), PERIOD_CURRENT, ATR_Period);
    else
        atr_handle = iMA(Symbol(), PERIOD_CURRENT, ATR_Period, 0, MODE_SMA, PRICE_TYPICAL);
    
    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator");
        return INIT_FAILED;
    }
    
    //--- Set indicator name
    IndicatorSetString(INDICATOR_SHORTNAME, "Supertrend(" + IntegerToString(ATR_Period) + "," + DoubleToString(ATR_Multiplier, 1) + ")");
    
    //--- Set precision
    IndicatorSetInteger(INDICATOR_DIGITS, Digits());
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                      |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handle
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
    //--- Check for minimum bars
    if(rates_total < ATR_Period + 1)
        return 0;
    
    //--- Set arrays as series
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);
    ArraySetAsSeries(time, true);
    
    //--- Get ATR values
    if(CopyBuffer(atr_handle, 0, 0, rates_total, ATRBuffer) <= 0)
        return 0;
    
    //--- Calculate start position
    int start = prev_calculated;
    if(start == 0)
    {
        start = ATR_Period;
        //--- Initialize first values
        TrendBuffer[rates_total - 1] = 1;
        SupertrendUpBuffer[rates_total - 1] = EMPTY_VALUE;
        SupertrendDownBuffer[rates_total - 1] = EMPTY_VALUE;
    }
    
    //--- Main calculation loop
    for(int i = start; i < rates_total; i++)
    {
        int pos = rates_total - 1 - i;
        
        //--- Calculate source price (HL2)
        double src = 0;
        switch(Source_Price)
        {
            case PRICE_CLOSE: src = close[pos]; break;
            case PRICE_OPEN: src = open[pos]; break;
            case PRICE_HIGH: src = high[pos]; break;
            case PRICE_LOW: src = low[pos]; break;
            case PRICE_MEDIAN: src = (high[pos] + low[pos]) / 2.0; break;
            case PRICE_TYPICAL: src = (high[pos] + low[pos] + close[pos]) / 3.0; break;
            case PRICE_WEIGHTED: src = (high[pos] + low[pos] + 2 * close[pos]) / 4.0; break;
            default: src = (high[pos] + low[pos]) / 2.0; break;
        }
        
        //--- Get ATR value
        double atr = ATRBuffer[pos];
        
        //--- Calculate basic upper and lower bands
        double up = src - (ATR_Multiplier * atr);
        double dn = src + (ATR_Multiplier * atr);
        
        //--- Apply Supertrend logic
        double up_prev = (pos < rates_total - 1) ? 
                        ((SupertrendUpBuffer[pos + 1] != EMPTY_VALUE) ? SupertrendUpBuffer[pos + 1] : up) : up;
        double dn_prev = (pos < rates_total - 1) ? 
                        ((SupertrendDownBuffer[pos + 1] != EMPTY_VALUE) ? SupertrendDownBuffer[pos + 1] : dn) : dn;
        
        if(pos < rates_total - 1)
        {
            up = (close[pos + 1] > up_prev) ? MathMax(up, up_prev) : up;
            dn = (close[pos + 1] < dn_prev) ? MathMin(dn, dn_prev) : dn;
        }
        
        //--- Determine trend
        int trend = (pos < rates_total - 1) ? (int)TrendBuffer[pos + 1] : 1;
        
        if(trend == -1 && close[pos] > dn_prev)
            trend = 1;
        else if(trend == 1 && close[pos] < up_prev)
            trend = -1;
        
        TrendBuffer[pos] = trend;
        
        //--- Set buffer values
        if(trend == 1)
        {
            SupertrendUpBuffer[pos] = up;
            SupertrendDownBuffer[pos] = EMPTY_VALUE;
        }
        else
        {
            SupertrendUpBuffer[pos] = EMPTY_VALUE;
            SupertrendDownBuffer[pos] = dn;
        }
        
        //--- Draw signals if enabled
        if(Show_Signals && pos < rates_total - 1)
        {
            int prev_trend_val = (int)TrendBuffer[pos + 1];
            
            //--- Buy signal
            if(trend == 1 && prev_trend_val == -1)
            {
                DrawBuySignal(time[pos], up);
            }
            //--- Sell signal
            else if(trend == -1 && prev_trend_val == 1)
            {
                DrawSellSignal(time[pos], dn);
            }
        }
    }
    
    return rates_total;
}

//+------------------------------------------------------------------+
//| Draw Buy Signal                                                 |
//+------------------------------------------------------------------+
void DrawBuySignal(datetime signal_time, double price)
{
    string signal_name = "Buy_Signal_" + IntegerToString(signal_time);
    
    if(ObjectCreate(0, signal_name, OBJ_ARROW_BUY, 0, signal_time, price))
    {
        ObjectSetInteger(0, signal_name, OBJPROP_COLOR, clrLime);
        ObjectSetInteger(0, signal_name, OBJPROP_WIDTH, 3);
        ObjectSetInteger(0, signal_name, OBJPROP_BACK, false);
    }
}

//+------------------------------------------------------------------+
//| Draw Sell Signal                                               |
//+------------------------------------------------------------------+
void DrawSellSignal(datetime signal_time, double price)
{
    string signal_name = "Sell_Signal_" + IntegerToString(signal_time);
    
    if(ObjectCreate(0, signal_name, OBJ_ARROW_SELL, 0, signal_time, price))
    {
        ObjectSetInteger(0, signal_name, OBJPROP_COLOR, clrRed);
        ObjectSetInteger(0, signal_name, OBJPROP_WIDTH, 3);
        ObjectSetInteger(0, signal_name, OBJPROP_BACK, false);
    }
}
