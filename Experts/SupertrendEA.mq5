//+------------------------------------------------------------------+
//|                                                SupertrendEA.mq5 |
//|                                  Copyright 2024, Supertrend EA  |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Supertrend EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Supertrend Expert Advisor with Dashboard and Multi-Timeframe Scanner"

//--- Include files
#include <Trade\Trade.mqh>
#include <Controls\Dialog.mqh>
#include <Controls\Label.mqh>
#include <Controls\Panel.mqh>

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+

//--- Supertrend Settings
input group "📊 SUPERTREND SETTINGS"
input int                ATR_Period = 10;                    // ATR Period
input double             ATR_Multiplier = 3.0;              // ATR Multiplier
input bool               Use_Smoothed_ATR = true;           // Use Smoothed ATR (true=ATR, false=SMA)
input ENUM_APPLIED_PRICE Source_Price = PRICE_MEDIAN;       // Source Price (HL2)

//--- Trading Settings
input group "💰 TRADING SETTINGS"
input double             Lot_Size = 0.1;                    // Lot Size
input int                Take_Profit = 500;                 // Take Profit (points)
input int                Stop_Loss = 300;                   // Stop Loss (points)
input bool               Enable_Trailing_Stop = false;      // Enable Trailing Stop
input int                Trailing_Stop_Distance = 200;      // Trailing Stop Distance (points)
input int                Trailing_Stop_Step = 50;           // Trailing Stop Step (points)
input int                Magic_Number = 12345;              // Magic Number
input string             Trade_Comment = "Supertrend EA";   // Trade Comment
input bool               Enable_Auto_Trading = true;        // Enable Auto Trading
input bool               Allow_Hedging = true;              // Allow Multiple Positions

//--- Dashboard Settings
input group "📋 DASHBOARD SETTINGS"
input bool               Show_Dashboard = true;             // Show Dashboard
input int                Dashboard_X = 20;                  // Dashboard X Position
input int                Dashboard_Y = 50;                  // Dashboard Y Position
input int                Dashboard_Width = 280;             // Dashboard Width
input int                Dashboard_Height = 180;            // Dashboard Height
input color              Dashboard_Background = C'240,240,240'; // Dashboard Background Color
input color              Dashboard_Border = clrDarkGray;    // Dashboard Border Color
input int                Dashboard_Font_Size = 9;           // Dashboard Font Size

//--- Scanner Settings
input group "🔍 MULTI-SYMBOL SCANNER SETTINGS"
input bool               Enable_Scanner = true;             // Enable Scanner
input string             Scanner_Symbols = "EURUSD,GBPUSD,USDJPY,AUDUSD,USDCAD,NZDUSD"; // Symbols (comma separated)
input string             Scanner_Timeframes = "M1,M5,M15,H1,H4,D1,W1"; // Timeframes (comma separated)
input int                Scanner_X = 320;                   // Scanner X Position
input int                Scanner_Y = 50;                    // Scanner Y Position
input int                Scanner_Width = 650;               // Scanner Width
input int                Scanner_Height = 300;              // Scanner Height
input int                Scanner_Update_Seconds = 5;        // Update Frequency (seconds)
input color              Scanner_Background = C'30,30,30';  // Scanner Background Color
input color              Scanner_Header_Color = clrNavy;    // Scanner Header Color
input int                Scanner_Font_Size = 8;             // Scanner Font Size

//--- Chart Visualization
input group "🎨 CHART VISUALIZATION"
input bool               Show_Buy_Sell_Signals = true;      // Show Buy/Sell Signals
input bool               Show_Trend_Lines = true;           // Show Supertrend Lines
input bool               Highlight_Trend_Zones = true;      // Highlight Trend Zones
input int                Signal_Arrow_Size = 2;             // Signal Arrow Size (1-5)
input int                Trend_Line_Width = 2;              // Trend Line Width
input color              Buy_Signal_Color = clrLime;        // Buy Signal Color
input color              Sell_Signal_Color = clrRed;        // Sell Signal Color
input color              Bullish_Zone_Color = C'230,255,230'; // Bullish Zone Color
input color              Bearish_Zone_Color = C'255,230,230'; // Bearish Zone Color
input bool               Show_Heiken_Ashi = false;          // Show Heiken Ashi Overlay

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade trade;
int atr_handle;

//--- Supertrend calculation arrays
double supertrend_up[], supertrend_down[], trend_buffer[];
double atr_buffer[];
double up_trend[], down_trend[];

//--- Current state variables
int current_trend = 0;
int previous_trend = 0;
datetime trend_start_time = 0;
datetime last_signal_time = 0;
int last_signal_type = 0; // 1 = Buy, -1 = Sell

//--- Previous values for Supertrend calculation
double prev_up = 0;
double prev_down = 0;

//--- Dashboard objects
string dashboard_objects[];
string scanner_objects[];

//--- Scanner variables
string symbol_list[];
string timeframe_list[];
ENUM_TIMEFRAMES timeframe_enums[];
int symbol_count = 0;
int timeframe_count = 0;

//--- Scanner indicator handles and values
double rsi_values[];
double stoch_values[];
double cci_values[];
double adx_values[];
double ao_values[];

//--- Timer variables
datetime last_update_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize trade object
    trade.SetExpertMagicNumber(Magic_Number);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    
    //--- Create ATR indicator for Supertrend calculation
    if(!InitializeSupertrendCalculation())
    {
        Print("Failed to initialize Supertrend calculation");
        return INIT_FAILED;
    }
    
    //--- Parse symbols and timeframes for scanner
    if(Enable_Scanner)
    {
        ParseSymbols();
        ParseTimeframes();
    }
    
    //--- Create dashboard
    if(Show_Dashboard)
    {
        CreateDashboard();
    }
    
    //--- Create scanner
    if(Enable_Scanner)
    {
        CreateScanner();
    }
    
    //--- Set timer for updates
    EventSetTimer(Scanner_Update_Seconds);
    
    //--- Initialize trend tracking
    trend_start_time = TimeCurrent();
    
    Print("Supertrend EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Kill timer
    EventKillTimer();
    
    //--- Clean up dashboard objects
    DeleteDashboard();
    
    //--- Clean up scanner objects
    DeleteScanner();
    
    //--- Release ATR indicator handle
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
    
    Print("Supertrend EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                            |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check if auto trading is enabled
    if(!Enable_Auto_Trading)
        return;
    
    //--- Calculate Supertrend values
    if(!CalculateSupertrendValues())
        return;
    
    //--- Check for trend changes and signals
    CheckTrendChange();
    
    //--- Check for trading signals
    if(CheckBuySignal())
    {
        if(Allow_Hedging || CountPositions(POSITION_TYPE_BUY) == 0)
        {
            OpenBuyOrder();
        }
    }
    else if(CheckSellSignal())
    {
        if(Allow_Hedging || CountPositions(POSITION_TYPE_SELL) == 0)
        {
            OpenSellOrder();
        }
    }
    
    //--- Manage existing trades
    if(Enable_Trailing_Stop)
    {
        ManageTrailingStop();
    }
    
    //--- Update dashboard on new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(Symbol(), PERIOD_CURRENT, 0);
    if(current_bar_time != last_bar_time)
    {
        if(Show_Dashboard)
            UpdateDashboard();
        last_bar_time = current_bar_time;
    }
}

//+------------------------------------------------------------------+
//| Timer function                                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
    //--- Update scanner
    if(Enable_Scanner)
    {
        UpdateScanner();
    }

    //--- Update dashboard
    if(Show_Dashboard)
    {
        UpdateDashboard();
    }
}

//+------------------------------------------------------------------+
//| Initialize Supertrend Calculation                               |
//+------------------------------------------------------------------+
bool InitializeSupertrendCalculation()
{
    //--- Create ATR indicator
    atr_handle = iATR(Symbol(), PERIOD_CURRENT, ATR_Period);

    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator");
        return false;
    }

    //--- Initialize arrays
    ArraySetAsSeries(supertrend_up, true);
    ArraySetAsSeries(supertrend_down, true);
    ArraySetAsSeries(trend_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(up_trend, true);
    ArraySetAsSeries(down_trend, true);

    //--- Resize arrays
    ArrayResize(supertrend_up, 1000);
    ArrayResize(supertrend_down, 1000);
    ArrayResize(trend_buffer, 1000);
    ArrayResize(atr_buffer, 1000);
    ArrayResize(up_trend, 1000);
    ArrayResize(down_trend, 1000);

    //--- Initialize values
    prev_up = 0;
    prev_down = 0;
    current_trend = 1; // Start with bullish trend

    return true;
}

//+------------------------------------------------------------------+
//| Calculate Supertrend Values                                     |
//+------------------------------------------------------------------+
bool CalculateSupertrendValues()
{
    //--- Get ATR values
    if(CopyBuffer(atr_handle, 0, 0, 3, atr_buffer) < 3)
    {
        Print("Failed to copy ATR values");
        return false;
    }

    //--- Get price data
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    if(CopyHigh(Symbol(), PERIOD_CURRENT, 0, 3, high) < 3 ||
       CopyLow(Symbol(), PERIOD_CURRENT, 0, 3, low) < 3 ||
       CopyClose(Symbol(), PERIOD_CURRENT, 0, 3, close) < 3)
    {
        Print("Failed to copy price data");
        return false;
    }

    //--- Calculate source price based on input parameter
    double src = 0;
    switch(Source_Price)
    {
        case PRICE_CLOSE: src = close[0]; break;
        case PRICE_OPEN: src = iOpen(Symbol(), PERIOD_CURRENT, 0); break;
        case PRICE_HIGH: src = high[0]; break;
        case PRICE_LOW: src = low[0]; break;
        case PRICE_MEDIAN: src = (high[0] + low[0]) / 2.0; break;
        case PRICE_TYPICAL: src = (high[0] + low[0] + close[0]) / 3.0; break;
        case PRICE_WEIGHTED: src = (high[0] + low[0] + 2 * close[0]) / 4.0; break;
        default: src = (high[0] + low[0]) / 2.0; break;
    }

    //--- Get ATR value
    double atr = atr_buffer[0];

    //--- Calculate basic upper and lower bands
    double up = src - (ATR_Multiplier * atr);
    double dn = src + (ATR_Multiplier * atr);

    //--- Apply Supertrend logic
    if(prev_up == 0) prev_up = up;
    if(prev_down == 0) prev_down = dn;

    up = (close[1] > prev_up) ? MathMax(up, prev_up) : up;
    dn = (close[1] < prev_down) ? MathMin(dn, prev_down) : dn;

    //--- Store current values
    up_trend[0] = up;
    down_trend[0] = dn;

    //--- Determine trend
    previous_trend = current_trend;

    if(current_trend == -1 && close[0] > prev_down)
        current_trend = 1;
    else if(current_trend == 1 && close[0] < prev_up)
        current_trend = -1;

    //--- Store trend
    trend_buffer[0] = current_trend;

    //--- Set Supertrend values based on trend
    if(current_trend == 1)
    {
        supertrend_up[0] = up;
        supertrend_down[0] = EMPTY_VALUE;
    }
    else
    {
        supertrend_up[0] = EMPTY_VALUE;
        supertrend_down[0] = dn;
    }

    //--- Update previous values for next calculation
    prev_up = up;
    prev_down = dn;

    //--- Draw trend lines if enabled
    if(Show_Trend_Lines)
    {
        DrawTrendLines(up, dn);
    }

    //--- Draw signals if enabled
    if(Show_Buy_Sell_Signals)
    {
        DrawSignals();
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check for trend change                                          |
//+------------------------------------------------------------------+
void CheckTrendChange()
{
    if(current_trend != previous_trend && previous_trend != 0)
    {
        trend_start_time = TimeCurrent();
        last_signal_time = TimeCurrent();
        last_signal_type = current_trend;

        Print("Trend changed from ", previous_trend, " to ", current_trend);
    }
}

//+------------------------------------------------------------------+
//| Check Buy Signal                                                |
//+------------------------------------------------------------------+
bool CheckBuySignal()
{
    return (current_trend == 1 && previous_trend == -1);
}

//+------------------------------------------------------------------+
//| Check Sell Signal                                               |
//+------------------------------------------------------------------+
bool CheckSellSignal()
{
    return (current_trend == -1 && previous_trend == 1);
}

//+------------------------------------------------------------------+
//| Open Buy Order                                                  |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl = (Stop_Loss > 0) ? price - (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price + (Take_Profit * Point()) : 0;

    if(trade.Buy(Lot_Size, Symbol(), price, sl, tp, Trade_Comment))
    {
        Print("Buy order opened at ", price);
    }
    else
    {
        Print("Failed to open buy order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open Sell Order                                                 |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl = (Stop_Loss > 0) ? price + (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price - (Take_Profit * Point()) : 0;

    if(trade.Sell(Lot_Size, Symbol(), price, sl, tp, Trade_Comment))
    {
        Print("Sell order opened at ", price);
    }
    else
    {
        Print("Failed to open sell order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Count Positions                                                 |
//+------------------------------------------------------------------+
int CountPositions(ENUM_POSITION_TYPE type)
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == type)
        {
            count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Calculate Profit for Position Type                              |
//+------------------------------------------------------------------+
double CalculateProfit(ENUM_POSITION_TYPE type)
{
    double profit = 0.0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == type)
        {
            profit += PositionGetDouble(POSITION_PROFIT);
        }
    }
    return profit;
}

//+------------------------------------------------------------------+
//| Manage Trailing Stop                                            |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_sl = PositionGetDouble(POSITION_SL);
            double current_price = (pos_type == POSITION_TYPE_BUY) ?
                                   SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                                   SymbolInfoDouble(Symbol(), SYMBOL_ASK);

            double new_sl = 0;
            bool modify = false;

            if(pos_type == POSITION_TYPE_BUY)
            {
                new_sl = current_price - (Trailing_Stop_Distance * Point());
                if(new_sl > current_sl + (Trailing_Stop_Step * Point()) || current_sl == 0)
                {
                    modify = true;
                }
            }
            else if(pos_type == POSITION_TYPE_SELL)
            {
                new_sl = current_price + (Trailing_Stop_Distance * Point());
                if(new_sl < current_sl - (Trailing_Stop_Step * Point()) || current_sl == 0)
                {
                    modify = true;
                }
            }

            if(modify)
            {
                double tp = PositionGetDouble(POSITION_TP);
                if(trade.PositionModify(ticket, new_sl, tp))
                {
                    Print("Trailing stop updated for position ", ticket, " New SL: ", new_sl);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Parse Symbols from Input String                                 |
//+------------------------------------------------------------------+
void ParseSymbols()
{
    string symbol_string = Scanner_Symbols;
    string separator = ",";

    //--- Clear array
    ArrayFree(symbol_list);

    //--- Split string
    string parts[];
    int count = StringSplit(symbol_string, StringGetCharacter(separator, 0), parts);

    ArrayResize(symbol_list, count);

    for(int i = 0; i < count; i++)
    {
        StringTrimLeft(parts[i]);
        StringTrimRight(parts[i]);
        symbol_list[i] = parts[i];
    }

    symbol_count = count;
}

//+------------------------------------------------------------------+
//| Parse Timeframes from Input String                              |
//+------------------------------------------------------------------+
void ParseTimeframes()
{
    string tf_string = Scanner_Timeframes;
    string separator = ",";

    //--- Clear arrays
    ArrayFree(timeframe_list);
    ArrayFree(timeframe_enums);

    //--- Split string
    string parts[];
    int count = StringSplit(tf_string, StringGetCharacter(separator, 0), parts);

    ArrayResize(timeframe_list, count);
    ArrayResize(timeframe_enums, count);

    for(int i = 0; i < count; i++)
    {
        StringTrimLeft(parts[i]);
        StringTrimRight(parts[i]);
        timeframe_list[i] = parts[i];
        timeframe_enums[i] = StringToTimeframe(parts[i]);
    }

    timeframe_count = count;
}

//+------------------------------------------------------------------+
//| Convert String to Timeframe                                     |
//+------------------------------------------------------------------+
ENUM_TIMEFRAMES StringToTimeframe(string tf_str)
{
    if(tf_str == "M1") return PERIOD_M1;
    if(tf_str == "M5") return PERIOD_M5;
    if(tf_str == "M15") return PERIOD_M15;
    if(tf_str == "M30") return PERIOD_M30;
    if(tf_str == "H1") return PERIOD_H1;
    if(tf_str == "H4") return PERIOD_H4;
    if(tf_str == "D1") return PERIOD_D1;
    if(tf_str == "W1") return PERIOD_W1;
    if(tf_str == "MN1") return PERIOD_MN1;

    return PERIOD_CURRENT;
}

//+------------------------------------------------------------------+
//| Create Dashboard                                                |
//+------------------------------------------------------------------+
void CreateDashboard()
{
    //--- Dashboard background
    string bg_name = "Dashboard_Background";
    if(ObjectCreate(0, bg_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, bg_name, OBJPROP_XDISTANCE, Dashboard_X);
        ObjectSetInteger(0, bg_name, OBJPROP_YDISTANCE, Dashboard_Y);
        ObjectSetInteger(0, bg_name, OBJPROP_XSIZE, Dashboard_Width);
        ObjectSetInteger(0, bg_name, OBJPROP_YSIZE, Dashboard_Height);
        ObjectSetInteger(0, bg_name, OBJPROP_BGCOLOR, Dashboard_Background);
        ObjectSetInteger(0, bg_name, OBJPROP_BORDER_COLOR, Dashboard_Border);
        ObjectSetInteger(0, bg_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, bg_name, OBJPROP_BACK, false);
    }

    //--- Dashboard title
    string title_name = "Dashboard_Title";
    if(ObjectCreate(0, title_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, title_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, title_name, OBJPROP_YDISTANCE, Dashboard_Y + 10);
        ObjectSetString(0, title_name, OBJPROP_TEXT, "📊 SUPERTREND DASHBOARD");
        ObjectSetString(0, title_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, title_name, OBJPROP_FONTSIZE, Dashboard_Font_Size + 1);
        ObjectSetInteger(0, title_name, OBJPROP_COLOR, clrNavy);
        ObjectSetInteger(0, title_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Current trend label
    string trend_name = "Dashboard_Trend";
    if(ObjectCreate(0, trend_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, trend_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, trend_name, OBJPROP_YDISTANCE, Dashboard_Y + 35);
        ObjectSetString(0, trend_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, trend_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, trend_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Trend duration label
    string duration_name = "Dashboard_Duration";
    if(ObjectCreate(0, duration_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, duration_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, duration_name, OBJPROP_YDISTANCE, Dashboard_Y + 55);
        ObjectSetString(0, duration_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, duration_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, duration_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, duration_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Last signal label
    string signal_name = "Dashboard_Signal";
    if(ObjectCreate(0, signal_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, signal_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, signal_name, OBJPROP_YDISTANCE, Dashboard_Y + 75);
        ObjectSetString(0, signal_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, signal_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, signal_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, signal_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Active trades label
    string trades_name = "Dashboard_Trades";
    if(ObjectCreate(0, trades_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, trades_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, trades_name, OBJPROP_YDISTANCE, Dashboard_Y + 95);
        ObjectSetString(0, trades_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, trades_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, trades_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, trades_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- P&L label
    string pl_name = "Dashboard_PL";
    if(ObjectCreate(0, pl_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, pl_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, pl_name, OBJPROP_YDISTANCE, Dashboard_Y + 115);
        ObjectSetString(0, pl_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, pl_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, pl_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Total P&L label
    string total_pl_name = "Dashboard_Total_PL";
    if(ObjectCreate(0, total_pl_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, total_pl_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, total_pl_name, OBJPROP_YDISTANCE, Dashboard_Y + 135);
        ObjectSetString(0, total_pl_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, total_pl_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, total_pl_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
}

//+------------------------------------------------------------------+
//| Update Dashboard                                                |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    //--- Update trend display
    string trend_text = "Current Trend: ";
    color trend_color = clrGray;

    if(current_trend == 1)
    {
        trend_text += "🟢 BUY";
        trend_color = clrGreen;
    }
    else if(current_trend == -1)
    {
        trend_text += "🔴 SELL";
        trend_color = clrRed;
    }
    else
    {
        trend_text += "🟡 NEUTRAL";
        trend_color = clrOrange;
    }

    ObjectSetString(0, "Dashboard_Trend", OBJPROP_TEXT, trend_text);
    ObjectSetInteger(0, "Dashboard_Trend", OBJPROP_COLOR, trend_color);

    //--- Update trend duration
    string duration_text = "Trend Duration: " + FormatDuration(TimeCurrent() - trend_start_time);
    ObjectSetString(0, "Dashboard_Duration", OBJPROP_TEXT, duration_text);

    //--- Update last signal
    string signal_text = "Last Signal: ";
    if(last_signal_type == 1)
        signal_text += "BUY at " + TimeToString(last_signal_time, TIME_MINUTES);
    else if(last_signal_type == -1)
        signal_text += "SELL at " + TimeToString(last_signal_time, TIME_MINUTES);
    else
        signal_text += "None";

    ObjectSetString(0, "Dashboard_Signal", OBJPROP_TEXT, signal_text);

    //--- Update active trades
    int buy_count = CountPositions(POSITION_TYPE_BUY);
    int sell_count = CountPositions(POSITION_TYPE_SELL);
    string trades_text = StringFormat("Active Trades: %d Buy | %d Sell", buy_count, sell_count);
    ObjectSetString(0, "Dashboard_Trades", OBJPROP_TEXT, trades_text);

    //--- Update P&L
    double buy_profit = CalculateProfit(POSITION_TYPE_BUY);
    double sell_profit = CalculateProfit(POSITION_TYPE_SELL);
    double total_profit = buy_profit + sell_profit;

    string pl_text = StringFormat("P&L: Buy $%.2f | Sell $%.2f", buy_profit, sell_profit);
    ObjectSetString(0, "Dashboard_PL", OBJPROP_TEXT, pl_text);

    string total_pl_text = StringFormat("Total P&L: $%.2f", total_profit);
    color pl_color = (total_profit >= 0) ? clrGreen : clrRed;
    ObjectSetString(0, "Dashboard_Total_PL", OBJPROP_TEXT, total_pl_text);
    ObjectSetInteger(0, "Dashboard_Total_PL", OBJPROP_COLOR, pl_color);
}

//+------------------------------------------------------------------+
//| Delete Dashboard                                                |
//+------------------------------------------------------------------+
void DeleteDashboard()
{
    ObjectDelete(0, "Dashboard_Background");
    ObjectDelete(0, "Dashboard_Title");
    ObjectDelete(0, "Dashboard_Trend");
    ObjectDelete(0, "Dashboard_Duration");
    ObjectDelete(0, "Dashboard_Signal");
    ObjectDelete(0, "Dashboard_Trades");
    ObjectDelete(0, "Dashboard_PL");
    ObjectDelete(0, "Dashboard_Total_PL");
}

//+------------------------------------------------------------------+
//| Format Duration                                                 |
//+------------------------------------------------------------------+
string FormatDuration(int seconds)
{
    int hours = seconds / 3600;
    int minutes = (seconds % 3600) / 60;

    if(hours > 0)
        return StringFormat("%dh %dm", hours, minutes);
    else
        return StringFormat("%dm", minutes);
}

//+------------------------------------------------------------------+
//| Create Multi-Symbol Scanner                                     |
//+------------------------------------------------------------------+
void CreateScanner()
{
    if(symbol_count == 0 || timeframe_count == 0) return;

    int cell_width = 70;
    int cell_height = 20;
    int header_height = 25;
    int total_width = 90 + (timeframe_count * cell_width) + 180; // Symbol + timeframes + indicators
    int total_height = header_height + (symbol_count * cell_height) + 30;

    //--- Scanner main panel
    string bg_name = "Scanner_Background";
    if(ObjectCreate(0, bg_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, bg_name, OBJPROP_XDISTANCE, Scanner_X);
        ObjectSetInteger(0, bg_name, OBJPROP_YDISTANCE, Scanner_Y);
        ObjectSetInteger(0, bg_name, OBJPROP_XSIZE, total_width);
        ObjectSetInteger(0, bg_name, OBJPROP_YSIZE, total_height);
        ObjectSetInteger(0, bg_name, OBJPROP_BGCOLOR, Scanner_Background);
        ObjectSetInteger(0, bg_name, OBJPROP_BORDER_COLOR, clrDarkGray);
        ObjectSetInteger(0, bg_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, bg_name, OBJPROP_BACK, false);
    }

    //--- Scanner header panel
    string header_bg_name = "Scanner_Header_Background";
    if(ObjectCreate(0, header_bg_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, header_bg_name, OBJPROP_XDISTANCE, Scanner_X);
        ObjectSetInteger(0, header_bg_name, OBJPROP_YDISTANCE, Scanner_Y);
        ObjectSetInteger(0, header_bg_name, OBJPROP_XSIZE, total_width);
        ObjectSetInteger(0, header_bg_name, OBJPROP_YSIZE, header_height);
        ObjectSetInteger(0, header_bg_name, OBJPROP_BGCOLOR, C'60,60,60');
        ObjectSetInteger(0, header_bg_name, OBJPROP_BORDER_COLOR, clrDarkGray);
        ObjectSetInteger(0, header_bg_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, header_bg_name, OBJPROP_BACK, false);
    }

    //--- Scanner title
    string title_name = "Scanner_Title";
    if(ObjectCreate(0, title_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, title_name, OBJPROP_XDISTANCE, Scanner_X + 10);
        ObjectSetInteger(0, title_name, OBJPROP_YDISTANCE, Scanner_Y + 8);
        ObjectSetString(0, title_name, OBJPROP_TEXT, "📊 MULTI-SYMBOL SCANNER");
        ObjectSetString(0, title_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, title_name, OBJPROP_FONTSIZE, Scanner_Font_Size + 1);
        ObjectSetInteger(0, title_name, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, title_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Close button
    string close_btn_name = "Scanner_Close_Button";
    if(ObjectCreate(0, close_btn_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, close_btn_name, OBJPROP_XDISTANCE, Scanner_X + total_width - 20);
        ObjectSetInteger(0, close_btn_name, OBJPROP_YDISTANCE, Scanner_Y + 8);
        ObjectSetString(0, close_btn_name, OBJPROP_TEXT, "✕");
        ObjectSetString(0, close_btn_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, close_btn_name, OBJPROP_FONTSIZE, 12);
        ObjectSetInteger(0, close_btn_name, OBJPROP_COLOR, clrYellow);
        ObjectSetInteger(0, close_btn_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    CreateScannerGrid();
}

//+------------------------------------------------------------------+
//| Create Scanner Grid                                             |
//+------------------------------------------------------------------+
void CreateScannerGrid()
{
    int cell_width = 70;
    int cell_height = 20;
    int start_x = Scanner_X + 10;
    int start_y = Scanner_Y + 30;

    //--- Create column headers
    string headers[] = {"Symbol", "BUY", "SELL", "RSI", "STOCH", "CCI", "ADX", "AO"};
    int header_widths[] = {80, 70, 70, 50, 50, 50, 50, 50};

    int x_pos = start_x;
    for(int h = 0; h < ArraySize(headers); h++)
    {
        string header_name = "Scanner_Header_" + IntegerToString(h);
        if(ObjectCreate(0, header_name, OBJ_LABEL, 0, 0, 0))
        {
            ObjectSetInteger(0, header_name, OBJPROP_XDISTANCE, x_pos);
            ObjectSetInteger(0, header_name, OBJPROP_YDISTANCE, start_y);
            ObjectSetString(0, header_name, OBJPROP_TEXT, headers[h]);
            ObjectSetString(0, header_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, header_name, OBJPROP_FONTSIZE, Scanner_Font_Size);
            ObjectSetInteger(0, header_name, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, header_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        }
        x_pos += header_widths[h];
    }

    //--- Create symbol rows and cells
    for(int s = 0; s < symbol_count; s++)
    {
        int y_pos = start_y + 25 + (s * cell_height);
        x_pos = start_x;

        //--- Symbol name
        string symbol_name = "Scanner_Symbol_" + IntegerToString(s);
        if(ObjectCreate(0, symbol_name, OBJ_LABEL, 0, 0, 0))
        {
            ObjectSetInteger(0, symbol_name, OBJPROP_XDISTANCE, x_pos);
            ObjectSetInteger(0, symbol_name, OBJPROP_YDISTANCE, y_pos);
            ObjectSetString(0, symbol_name, OBJPROP_TEXT, symbol_list[s]);
            ObjectSetString(0, symbol_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, symbol_name, OBJPROP_FONTSIZE, Scanner_Font_Size);
            ObjectSetInteger(0, symbol_name, OBJPROP_COLOR, clrAqua);
            ObjectSetInteger(0, symbol_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        }
        x_pos += 80;

        //--- Create cells for each indicator
        string cell_types[] = {"BUY", "SELL", "RSI", "STOCH", "CCI", "ADX", "AO"};
        for(int c = 0; c < ArraySize(cell_types); c++)
        {
            string cell_name = "Scanner_" + cell_types[c] + "_" + IntegerToString(s);
            if(ObjectCreate(0, cell_name, OBJ_LABEL, 0, 0, 0))
            {
                ObjectSetInteger(0, cell_name, OBJPROP_XDISTANCE, x_pos);
                ObjectSetInteger(0, cell_name, OBJPROP_YDISTANCE, y_pos);
                ObjectSetString(0, cell_name, OBJPROP_TEXT, "-");
                ObjectSetString(0, cell_name, OBJPROP_FONT, "Arial");
                ObjectSetInteger(0, cell_name, OBJPROP_FONTSIZE, Scanner_Font_Size - 1);
                ObjectSetInteger(0, cell_name, OBJPROP_COLOR, clrWhite);
                ObjectSetInteger(0, cell_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
            }
            x_pos += (c < 2) ? 70 : 50; // BUY/SELL wider than indicators
        }
    }

    //--- Last update label
    string update_name = "Scanner_LastUpdate";
    if(ObjectCreate(0, update_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, update_name, OBJPROP_XDISTANCE, Scanner_X + 10);
        ObjectSetInteger(0, update_name, OBJPROP_YDISTANCE, start_y + 30 + (symbol_count * cell_height));
        ObjectSetString(0, update_name, OBJPROP_TEXT, "Last Update: " + TimeToString(TimeCurrent(), TIME_SECONDS));
        ObjectSetString(0, update_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, update_name, OBJPROP_FONTSIZE, Scanner_Font_Size - 1);
        ObjectSetInteger(0, update_name, OBJPROP_COLOR, clrGray);
        ObjectSetInteger(0, update_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
}



//+------------------------------------------------------------------+
//| Update Multi-Symbol Scanner                                     |
//+------------------------------------------------------------------+
void UpdateScanner()
{
    for(int s = 0; s < symbol_count; s++)
    {
        string symbol = symbol_list[s];

        //--- Get indicator values for current symbol (using H1 timeframe as primary)
        ENUM_TIMEFRAMES primary_tf = PERIOD_H1;

        //--- Get RSI
        int rsi_handle = iRSI(symbol, primary_tf, 14, PRICE_CLOSE);
        double rsi_value = 0;
        if(rsi_handle != INVALID_HANDLE)
        {
            ArraySetAsSeries(rsi_values, true);
            if(CopyBuffer(rsi_handle, 0, 0, 1, rsi_values) > 0)
                rsi_value = rsi_values[0];
            IndicatorRelease(rsi_handle);
        }

        //--- Get Stochastic
        int stoch_handle = iStochastic(symbol, primary_tf, 14, 3, 3, MODE_SMA, STO_LOWHIGH);
        double stoch_value = 0;
        if(stoch_handle != INVALID_HANDLE)
        {
            ArraySetAsSeries(stoch_values, true);
            if(CopyBuffer(stoch_handle, 0, 0, 1, stoch_values) > 0)
                stoch_value = stoch_values[0];
            IndicatorRelease(stoch_handle);
        }

        //--- Get CCI
        int cci_handle = iCCI(symbol, primary_tf, 20, PRICE_TYPICAL);
        double cci_value = 0;
        if(cci_handle != INVALID_HANDLE)
        {
            ArraySetAsSeries(cci_values, true);
            if(CopyBuffer(cci_handle, 0, 0, 1, cci_values) > 0)
                cci_value = cci_values[0];
            IndicatorRelease(cci_handle);
        }

        //--- Get ADX
        int adx_handle = iADX(symbol, primary_tf, 14);
        double adx_value = 0;
        if(adx_handle != INVALID_HANDLE)
        {
            ArraySetAsSeries(adx_values, true);
            if(CopyBuffer(adx_handle, 0, 0, 1, adx_values) > 0)
                adx_value = adx_values[0];
            IndicatorRelease(adx_handle);
        }

        //--- Get AO
        int ao_handle = iAO(symbol, primary_tf);
        double ao_value = 0;
        if(ao_handle != INVALID_HANDLE)
        {
            ArraySetAsSeries(ao_values, true);
            if(CopyBuffer(ao_handle, 0, 0, 1, ao_values) > 0)
                ao_value = ao_values[0];
            IndicatorRelease(ao_handle);
        }

        //--- Calculate signals
        string buy_signal = CalculateSignalStrength(rsi_value, stoch_value, cci_value, adx_value, ao_value, true);
        string sell_signal = CalculateSignalStrength(rsi_value, stoch_value, cci_value, adx_value, ao_value, false);

        //--- Update display
        UpdateScannerCell("Scanner_BUY_" + IntegerToString(s), buy_signal, GetSignalColor(buy_signal, true));
        UpdateScannerCell("Scanner_SELL_" + IntegerToString(s), sell_signal, GetSignalColor(sell_signal, false));
        UpdateScannerCell("Scanner_RSI_" + IntegerToString(s), DoubleToString(rsi_value, 1), GetIndicatorColor(rsi_value, 30, 70));
        UpdateScannerCell("Scanner_STOCH_" + IntegerToString(s), DoubleToString(stoch_value, 1), GetIndicatorColor(stoch_value, 20, 80));
        UpdateScannerCell("Scanner_CCI_" + IntegerToString(s), DoubleToString(cci_value, 1), GetIndicatorColor(cci_value, -100, 100));
        UpdateScannerCell("Scanner_ADX_" + IntegerToString(s), DoubleToString(adx_value, 1), (adx_value > 25) ? clrLime : clrWhite);
        UpdateScannerCell("Scanner_AO_" + IntegerToString(s), DoubleToString(ao_value, 5), (ao_value > 0) ? clrLime : clrRed);
    }

    //--- Update last update time
    string update_text = "Last Update: " + TimeToString(TimeCurrent(), TIME_SECONDS);
    ObjectSetString(0, "Scanner_LastUpdate", OBJPROP_TEXT, update_text);
}

//+------------------------------------------------------------------+
//| Calculate Signal Strength                                       |
//+------------------------------------------------------------------+
string CalculateSignalStrength(double rsi, double stoch, double cci, double adx, double ao, bool is_buy)
{
    int signal_strength = 0;

    if(is_buy && rsi < 40) signal_strength++;
    else if(!is_buy && rsi > 60) signal_strength++;

    if(is_buy && stoch < 40) signal_strength++;
    else if(!is_buy && stoch > 60) signal_strength++;

    if(is_buy && cci < -70) signal_strength++;
    else if(!is_buy && cci > 70) signal_strength++;

    if(adx > 40) signal_strength++;

    if(is_buy && ao > 0) signal_strength++;
    else if(!is_buy && ao < 0) signal_strength++;

    if(signal_strength >= 3) return is_buy ? "Strong Buy" : "Strong Sell";
    if(signal_strength >= 2) return is_buy ? "Buy" : "Sell";
    return "Neutral";
}

//+------------------------------------------------------------------+
//| Get Signal Color                                                |
//+------------------------------------------------------------------+
color GetSignalColor(string signal, bool is_buy)
{
    if(signal == "Strong Buy" || signal == "Strong Sell") return is_buy ? clrLime : clrRed;
    if(signal == "Buy" || signal == "Sell") return is_buy ? clrGreen : clrOrange;
    return clrGray;
}

//+------------------------------------------------------------------+
//| Get Indicator Color                                             |
//+------------------------------------------------------------------+
color GetIndicatorColor(double value, double oversold, double overbought)
{
    if(value <= oversold) return clrLime;
    if(value >= overbought) return clrRed;
    return clrWhite;
}

//+------------------------------------------------------------------+
//| Update Scanner Cell                                             |
//+------------------------------------------------------------------+
void UpdateScannerCell(string object_name, string text, color text_color)
{
    if(ObjectFind(0, object_name) >= 0)
    {
        ObjectSetString(0, object_name, OBJPROP_TEXT, text);
        ObjectSetInteger(0, object_name, OBJPROP_COLOR, text_color);
    }
}



//+------------------------------------------------------------------+
//| Delete Scanner                                                  |
//+------------------------------------------------------------------+
void DeleteScanner()
{
    ObjectDelete(0, "Scanner_Background");
    ObjectDelete(0, "Scanner_Header_Background");
    ObjectDelete(0, "Scanner_Title");
    ObjectDelete(0, "Scanner_Close_Button");
    ObjectDelete(0, "Scanner_LastUpdate");

    //--- Delete headers
    for(int h = 0; h < 8; h++)
    {
        ObjectDelete(0, "Scanner_Header_" + IntegerToString(h));
    }

    //--- Delete symbol rows and cells
    for(int s = 0; s < symbol_count; s++)
    {
        ObjectDelete(0, "Scanner_Symbol_" + IntegerToString(s));
        ObjectDelete(0, "Scanner_BUY_" + IntegerToString(s));
        ObjectDelete(0, "Scanner_SELL_" + IntegerToString(s));
        ObjectDelete(0, "Scanner_RSI_" + IntegerToString(s));
        ObjectDelete(0, "Scanner_STOCH_" + IntegerToString(s));
        ObjectDelete(0, "Scanner_CCI_" + IntegerToString(s));
        ObjectDelete(0, "Scanner_ADX_" + IntegerToString(s));
        ObjectDelete(0, "Scanner_AO_" + IntegerToString(s));
    }
}

//+------------------------------------------------------------------+
//| Draw Trend Lines                                               |
//+------------------------------------------------------------------+
void DrawTrendLines(double up_value, double dn_value)
{
    if(!Show_Trend_Lines) return;

    datetime time_current = iTime(Symbol(), PERIOD_CURRENT, 0);
    datetime time_prev = iTime(Symbol(), PERIOD_CURRENT, 1);

    //--- Draw upper trend line (bullish)
    if(current_trend == 1)
    {
        string up_line_name = "Supertrend_Up_" + IntegerToString(time_current);
        if(ObjectCreate(0, up_line_name, OBJ_TREND, 0, time_prev, up_value, time_current, up_value))
        {
            ObjectSetInteger(0, up_line_name, OBJPROP_COLOR, clrGreen);
            ObjectSetInteger(0, up_line_name, OBJPROP_WIDTH, Trend_Line_Width);
            ObjectSetInteger(0, up_line_name, OBJPROP_RAY_RIGHT, false);
        }
    }

    //--- Draw lower trend line (bearish)
    if(current_trend == -1)
    {
        string dn_line_name = "Supertrend_Dn_" + IntegerToString(time_current);
        if(ObjectCreate(0, dn_line_name, OBJ_TREND, 0, time_prev, dn_value, time_current, dn_value))
        {
            ObjectSetInteger(0, dn_line_name, OBJPROP_COLOR, clrRed);
            ObjectSetInteger(0, dn_line_name, OBJPROP_WIDTH, Trend_Line_Width);
            ObjectSetInteger(0, dn_line_name, OBJPROP_RAY_RIGHT, false);
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Signals                                                   |
//+------------------------------------------------------------------+
void DrawSignals()
{
    if(!Show_Buy_Sell_Signals) return;

    datetime time_current = iTime(Symbol(), PERIOD_CURRENT, 0);

    //--- Draw buy signal
    if(CheckBuySignal())
    {
        double low_price = iLow(Symbol(), PERIOD_CURRENT, 0);
        string buy_arrow_name = "Buy_Signal_" + IntegerToString(time_current);

        if(ObjectCreate(0, buy_arrow_name, OBJ_ARROW_BUY, 0, time_current, low_price))
        {
            ObjectSetInteger(0, buy_arrow_name, OBJPROP_COLOR, Buy_Signal_Color);
            ObjectSetInteger(0, buy_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        }
    }

    //--- Draw sell signal
    if(CheckSellSignal())
    {
        double high_price = iHigh(Symbol(), PERIOD_CURRENT, 0);
        string sell_arrow_name = "Sell_Signal_" + IntegerToString(time_current);

        if(ObjectCreate(0, sell_arrow_name, OBJ_ARROW_SELL, 0, time_current, high_price))
        {
            ObjectSetInteger(0, sell_arrow_name, OBJPROP_COLOR, Sell_Signal_Color);
            ObjectSetInteger(0, sell_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        }
    }
}

//+------------------------------------------------------------------+
//| Chart Event Handler                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long& lparam, const double& dparam, const string& sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == "Scanner_Close_Button")
        {
            Print("Closing scanner panel");
            Enable_Scanner = false;
            DeleteScanner();
            ChartRedraw();
        }
    }
}
