//+------------------------------------------------------------------+
//|                                                SupertrendEA.mq5 |
//|                                  Copyright 2024, Supertrend EA  |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Supertrend EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Supertrend Expert Advisor with Dashboard and Multi-Timeframe Scanner"

//--- Include files
#include <Trade\Trade.mqh>
#include <Controls\Dialog.mqh>
#include <Controls\Label.mqh>
#include <Controls\Panel.mqh>

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+

//--- Supertrend Settings
input group "📊 SUPERTREND SETTINGS"
input int                ATR_Period = 10;                    // ATR Period
input double             ATR_Multiplier = 3.0;              // ATR Multiplier
input bool               Use_Smoothed_ATR = true;           // Use Smoothed ATR (true=ATR, false=SMA)
input ENUM_APPLIED_PRICE Source_Price = PRICE_MEDIAN;       // Source Price (HL2)

//--- Trading Settings
input group "💰 TRADING SETTINGS"
input double             Lot_Size = 0.1;                    // Lot Size
input int                Take_Profit = 500;                 // Take Profit (points)
input int                Stop_Loss = 300;                   // Stop Loss (points)
input bool               Enable_Trailing_Stop = false;      // Enable Trailing Stop
input int                Trailing_Stop_Distance = 200;      // Trailing Stop Distance (points)
input int                Trailing_Stop_Step = 50;           // Trailing Stop Step (points)
input int                Magic_Number = 12345;              // Magic Number
input string             Trade_Comment = "Supertrend EA";   // Trade Comment
input bool               Enable_Auto_Trading = true;        // Enable Auto Trading
input bool               Allow_Hedging = true;              // Allow Multiple Positions

//--- Dashboard Settings
input group "📋 DASHBOARD SETTINGS"
input bool               Show_Dashboard = true;             // Show Dashboard
input int                Dashboard_X = 20;                  // Dashboard X Position
input int                Dashboard_Y = 50;                  // Dashboard Y Position
input int                Dashboard_Width = 280;             // Dashboard Width
input int                Dashboard_Height = 180;            // Dashboard Height
input color              Dashboard_Background = C'240,240,240'; // Dashboard Background Color
input color              Dashboard_Border = clrDarkGray;    // Dashboard Border Color
input int                Dashboard_Font_Size = 9;           // Dashboard Font Size

//--- Scanner Settings
input group "🔍 SCANNER SETTINGS"
input bool               Enable_Scanner = true;             // Enable Scanner
input string             Scanner_Timeframes = "M1,M5,M15,H1,H4,D1,W1"; // Timeframes (comma separated)
input int                Scanner_X = 320;                   // Scanner X Position
input int                Scanner_Y = 50;                    // Scanner Y Position
input int                Scanner_Width = 420;               // Scanner Width
input int                Scanner_Height = 220;              // Scanner Height
input int                Scanner_Update_Seconds = 5;        // Update Frequency (seconds)
input color              Scanner_Background = C'250,250,250'; // Scanner Background Color
input color              Scanner_Header_Color = clrNavy;    // Scanner Header Color
input int                Scanner_Font_Size = 8;             // Scanner Font Size

//--- Chart Visualization
input group "🎨 CHART VISUALIZATION"
input bool               Show_Buy_Sell_Signals = true;      // Show Buy/Sell Signals
input bool               Show_Trend_Lines = true;           // Show Supertrend Lines
input bool               Highlight_Trend_Zones = true;      // Highlight Trend Zones
input int                Signal_Arrow_Size = 2;             // Signal Arrow Size (1-5)
input int                Trend_Line_Width = 2;              // Trend Line Width
input color              Buy_Signal_Color = clrLime;        // Buy Signal Color
input color              Sell_Signal_Color = clrRed;        // Sell Signal Color
input color              Bullish_Zone_Color = C'230,255,230'; // Bullish Zone Color
input color              Bearish_Zone_Color = C'255,230,230'; // Bearish Zone Color
input bool               Show_Heiken_Ashi = false;          // Show Heiken Ashi Overlay

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade trade;
int atr_handle;

//--- Supertrend calculation arrays
double supertrend_up[], supertrend_down[], trend_buffer[];
double atr_buffer[];
double up_trend[], down_trend[];

//--- Current state variables
int current_trend = 0;
int previous_trend = 0;
datetime trend_start_time = 0;
datetime last_signal_time = 0;
int last_signal_type = 0; // 1 = Buy, -1 = Sell

//--- Previous values for Supertrend calculation
double prev_up = 0;
double prev_down = 0;

//--- Dashboard objects
string dashboard_objects[];
string scanner_objects[];

//--- Scanner variables
string timeframe_list[];
ENUM_TIMEFRAMES timeframe_enums[];
int timeframe_count = 0;

//--- Timer variables
datetime last_update_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize trade object
    trade.SetExpertMagicNumber(Magic_Number);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    
    //--- Create ATR indicator for Supertrend calculation
    if(!InitializeSupertrendCalculation())
    {
        Print("Failed to initialize Supertrend calculation");
        return INIT_FAILED;
    }
    
    //--- Parse timeframes for scanner
    if(Enable_Scanner)
    {
        ParseTimeframes();
    }
    
    //--- Create dashboard
    if(Show_Dashboard)
    {
        CreateDashboard();
    }
    
    //--- Create scanner
    if(Enable_Scanner)
    {
        CreateScanner();
    }
    
    //--- Set timer for updates
    EventSetTimer(Scanner_Update_Seconds);
    
    //--- Initialize trend tracking
    trend_start_time = TimeCurrent();
    
    Print("Supertrend EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Kill timer
    EventKillTimer();
    
    //--- Clean up dashboard objects
    DeleteDashboard();
    
    //--- Clean up scanner objects
    DeleteScanner();
    
    //--- Release ATR indicator handle
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);
    
    Print("Supertrend EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                            |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check if auto trading is enabled
    if(!Enable_Auto_Trading)
        return;
    
    //--- Calculate Supertrend values
    if(!CalculateSupertrendValues())
        return;
    
    //--- Check for trend changes and signals
    CheckTrendChange();
    
    //--- Check for trading signals
    if(CheckBuySignal())
    {
        if(Allow_Hedging || CountPositions(POSITION_TYPE_BUY) == 0)
        {
            OpenBuyOrder();
        }
    }
    else if(CheckSellSignal())
    {
        if(Allow_Hedging || CountPositions(POSITION_TYPE_SELL) == 0)
        {
            OpenSellOrder();
        }
    }
    
    //--- Manage existing trades
    if(Enable_Trailing_Stop)
    {
        ManageTrailingStop();
    }
    
    //--- Update dashboard on new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(Symbol(), PERIOD_CURRENT, 0);
    if(current_bar_time != last_bar_time)
    {
        if(Show_Dashboard)
            UpdateDashboard();
        last_bar_time = current_bar_time;
    }
}

//+------------------------------------------------------------------+
//| Timer function                                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
    //--- Update scanner
    if(Enable_Scanner)
    {
        UpdateScanner();
    }

    //--- Update dashboard
    if(Show_Dashboard)
    {
        UpdateDashboard();
    }
}

//+------------------------------------------------------------------+
//| Initialize Supertrend Calculation                               |
//+------------------------------------------------------------------+
bool InitializeSupertrendCalculation()
{
    //--- Create ATR indicator
    atr_handle = iATR(Symbol(), PERIOD_CURRENT, ATR_Period);

    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator");
        return false;
    }

    //--- Initialize arrays
    ArraySetAsSeries(supertrend_up, true);
    ArraySetAsSeries(supertrend_down, true);
    ArraySetAsSeries(trend_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(up_trend, true);
    ArraySetAsSeries(down_trend, true);

    //--- Resize arrays
    ArrayResize(supertrend_up, 1000);
    ArrayResize(supertrend_down, 1000);
    ArrayResize(trend_buffer, 1000);
    ArrayResize(atr_buffer, 1000);
    ArrayResize(up_trend, 1000);
    ArrayResize(down_trend, 1000);

    //--- Initialize values
    prev_up = 0;
    prev_down = 0;
    current_trend = 1; // Start with bullish trend

    return true;
}

//+------------------------------------------------------------------+
//| Calculate Supertrend Values                                     |
//+------------------------------------------------------------------+
bool CalculateSupertrendValues()
{
    //--- Get ATR values
    if(CopyBuffer(atr_handle, 0, 0, 3, atr_buffer) < 3)
    {
        Print("Failed to copy ATR values");
        return false;
    }

    //--- Get price data
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    if(CopyHigh(Symbol(), PERIOD_CURRENT, 0, 3, high) < 3 ||
       CopyLow(Symbol(), PERIOD_CURRENT, 0, 3, low) < 3 ||
       CopyClose(Symbol(), PERIOD_CURRENT, 0, 3, close) < 3)
    {
        Print("Failed to copy price data");
        return false;
    }

    //--- Calculate source price based on input parameter
    double src = 0;
    switch(Source_Price)
    {
        case PRICE_CLOSE: src = close[0]; break;
        case PRICE_OPEN: src = iOpen(Symbol(), PERIOD_CURRENT, 0); break;
        case PRICE_HIGH: src = high[0]; break;
        case PRICE_LOW: src = low[0]; break;
        case PRICE_MEDIAN: src = (high[0] + low[0]) / 2.0; break;
        case PRICE_TYPICAL: src = (high[0] + low[0] + close[0]) / 3.0; break;
        case PRICE_WEIGHTED: src = (high[0] + low[0] + 2 * close[0]) / 4.0; break;
        default: src = (high[0] + low[0]) / 2.0; break;
    }

    //--- Get ATR value
    double atr = atr_buffer[0];

    //--- Calculate basic upper and lower bands
    double up = src - (ATR_Multiplier * atr);
    double dn = src + (ATR_Multiplier * atr);

    //--- Apply Supertrend logic
    if(prev_up == 0) prev_up = up;
    if(prev_down == 0) prev_down = dn;

    up = (close[1] > prev_up) ? MathMax(up, prev_up) : up;
    dn = (close[1] < prev_down) ? MathMin(dn, prev_down) : dn;

    //--- Store current values
    up_trend[0] = up;
    down_trend[0] = dn;

    //--- Determine trend
    previous_trend = current_trend;

    if(current_trend == -1 && close[0] > prev_down)
        current_trend = 1;
    else if(current_trend == 1 && close[0] < prev_up)
        current_trend = -1;

    //--- Store trend
    trend_buffer[0] = current_trend;

    //--- Set Supertrend values based on trend
    if(current_trend == 1)
    {
        supertrend_up[0] = up;
        supertrend_down[0] = EMPTY_VALUE;
    }
    else
    {
        supertrend_up[0] = EMPTY_VALUE;
        supertrend_down[0] = dn;
    }

    //--- Update previous values for next calculation
    prev_up = up;
    prev_down = dn;

    //--- Draw trend lines if enabled
    if(Show_Trend_Lines)
    {
        DrawTrendLines(up, dn);
    }

    //--- Draw signals if enabled
    if(Show_Buy_Sell_Signals)
    {
        DrawSignals();
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check for trend change                                          |
//+------------------------------------------------------------------+
void CheckTrendChange()
{
    if(current_trend != previous_trend && previous_trend != 0)
    {
        trend_start_time = TimeCurrent();
        last_signal_time = TimeCurrent();
        last_signal_type = current_trend;

        Print("Trend changed from ", previous_trend, " to ", current_trend);
    }
}

//+------------------------------------------------------------------+
//| Check Buy Signal                                                |
//+------------------------------------------------------------------+
bool CheckBuySignal()
{
    return (current_trend == 1 && previous_trend == -1);
}

//+------------------------------------------------------------------+
//| Check Sell Signal                                               |
//+------------------------------------------------------------------+
bool CheckSellSignal()
{
    return (current_trend == -1 && previous_trend == 1);
}

//+------------------------------------------------------------------+
//| Open Buy Order                                                  |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl = (Stop_Loss > 0) ? price - (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price + (Take_Profit * Point()) : 0;

    if(trade.Buy(Lot_Size, Symbol(), price, sl, tp, Trade_Comment))
    {
        Print("Buy order opened at ", price);
    }
    else
    {
        Print("Failed to open buy order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open Sell Order                                                 |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl = (Stop_Loss > 0) ? price + (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price - (Take_Profit * Point()) : 0;

    if(trade.Sell(Lot_Size, Symbol(), price, sl, tp, Trade_Comment))
    {
        Print("Sell order opened at ", price);
    }
    else
    {
        Print("Failed to open sell order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Count Positions                                                 |
//+------------------------------------------------------------------+
int CountPositions(ENUM_POSITION_TYPE type)
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == type)
        {
            count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Calculate Profit for Position Type                              |
//+------------------------------------------------------------------+
double CalculateProfit(ENUM_POSITION_TYPE type)
{
    double profit = 0.0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == type)
        {
            profit += PositionGetDouble(POSITION_PROFIT);
        }
    }
    return profit;
}

//+------------------------------------------------------------------+
//| Manage Trailing Stop                                            |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_sl = PositionGetDouble(POSITION_SL);
            double current_price = (pos_type == POSITION_TYPE_BUY) ?
                                   SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                                   SymbolInfoDouble(Symbol(), SYMBOL_ASK);

            double new_sl = 0;
            bool modify = false;

            if(pos_type == POSITION_TYPE_BUY)
            {
                new_sl = current_price - (Trailing_Stop_Distance * Point());
                if(new_sl > current_sl + (Trailing_Stop_Step * Point()) || current_sl == 0)
                {
                    modify = true;
                }
            }
            else if(pos_type == POSITION_TYPE_SELL)
            {
                new_sl = current_price + (Trailing_Stop_Distance * Point());
                if(new_sl < current_sl - (Trailing_Stop_Step * Point()) || current_sl == 0)
                {
                    modify = true;
                }
            }

            if(modify)
            {
                double tp = PositionGetDouble(POSITION_TP);
                if(trade.PositionModify(ticket, new_sl, tp))
                {
                    Print("Trailing stop updated for position ", ticket, " New SL: ", new_sl);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Parse Timeframes from Input String                              |
//+------------------------------------------------------------------+
void ParseTimeframes()
{
    string tf_string = Scanner_Timeframes;
    string separator = ",";

    //--- Clear arrays
    ArrayFree(timeframe_list);
    ArrayFree(timeframe_enums);

    //--- Split string
    string parts[];
    int count = StringSplit(tf_string, StringGetCharacter(separator, 0), parts);

    ArrayResize(timeframe_list, count);
    ArrayResize(timeframe_enums, count);

    for(int i = 0; i < count; i++)
    {
        StringTrimLeft(parts[i]);
        StringTrimRight(parts[i]);
        timeframe_list[i] = parts[i];
        timeframe_enums[i] = StringToTimeframe(parts[i]);
    }

    timeframe_count = count;
}

//+------------------------------------------------------------------+
//| Convert String to Timeframe                                     |
//+------------------------------------------------------------------+
ENUM_TIMEFRAMES StringToTimeframe(string tf_str)
{
    if(tf_str == "M1") return PERIOD_M1;
    if(tf_str == "M5") return PERIOD_M5;
    if(tf_str == "M15") return PERIOD_M15;
    if(tf_str == "M30") return PERIOD_M30;
    if(tf_str == "H1") return PERIOD_H1;
    if(tf_str == "H4") return PERIOD_H4;
    if(tf_str == "D1") return PERIOD_D1;
    if(tf_str == "W1") return PERIOD_W1;
    if(tf_str == "MN1") return PERIOD_MN1;

    return PERIOD_CURRENT;
}

//+------------------------------------------------------------------+
//| Create Dashboard                                                |
//+------------------------------------------------------------------+
void CreateDashboard()
{
    //--- Dashboard background
    string bg_name = "Dashboard_Background";
    if(ObjectCreate(0, bg_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, bg_name, OBJPROP_XDISTANCE, Dashboard_X);
        ObjectSetInteger(0, bg_name, OBJPROP_YDISTANCE, Dashboard_Y);
        ObjectSetInteger(0, bg_name, OBJPROP_XSIZE, Dashboard_Width);
        ObjectSetInteger(0, bg_name, OBJPROP_YSIZE, Dashboard_Height);
        ObjectSetInteger(0, bg_name, OBJPROP_BGCOLOR, Dashboard_Background);
        ObjectSetInteger(0, bg_name, OBJPROP_BORDER_COLOR, Dashboard_Border);
        ObjectSetInteger(0, bg_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, bg_name, OBJPROP_BACK, false);
    }

    //--- Dashboard title
    string title_name = "Dashboard_Title";
    if(ObjectCreate(0, title_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, title_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, title_name, OBJPROP_YDISTANCE, Dashboard_Y + 10);
        ObjectSetString(0, title_name, OBJPROP_TEXT, "📊 SUPERTREND DASHBOARD");
        ObjectSetString(0, title_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, title_name, OBJPROP_FONTSIZE, Dashboard_Font_Size + 1);
        ObjectSetInteger(0, title_name, OBJPROP_COLOR, clrNavy);
        ObjectSetInteger(0, title_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Current trend label
    string trend_name = "Dashboard_Trend";
    if(ObjectCreate(0, trend_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, trend_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, trend_name, OBJPROP_YDISTANCE, Dashboard_Y + 35);
        ObjectSetString(0, trend_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, trend_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, trend_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Trend duration label
    string duration_name = "Dashboard_Duration";
    if(ObjectCreate(0, duration_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, duration_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, duration_name, OBJPROP_YDISTANCE, Dashboard_Y + 55);
        ObjectSetString(0, duration_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, duration_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, duration_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, duration_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Last signal label
    string signal_name = "Dashboard_Signal";
    if(ObjectCreate(0, signal_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, signal_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, signal_name, OBJPROP_YDISTANCE, Dashboard_Y + 75);
        ObjectSetString(0, signal_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, signal_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, signal_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, signal_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Active trades label
    string trades_name = "Dashboard_Trades";
    if(ObjectCreate(0, trades_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, trades_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, trades_name, OBJPROP_YDISTANCE, Dashboard_Y + 95);
        ObjectSetString(0, trades_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, trades_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, trades_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, trades_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- P&L label
    string pl_name = "Dashboard_PL";
    if(ObjectCreate(0, pl_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, pl_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, pl_name, OBJPROP_YDISTANCE, Dashboard_Y + 115);
        ObjectSetString(0, pl_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, pl_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, pl_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Total P&L label
    string total_pl_name = "Dashboard_Total_PL";
    if(ObjectCreate(0, total_pl_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, total_pl_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, total_pl_name, OBJPROP_YDISTANCE, Dashboard_Y + 135);
        ObjectSetString(0, total_pl_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, total_pl_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, total_pl_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
}

//+------------------------------------------------------------------+
//| Update Dashboard                                                |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    //--- Update trend display
    string trend_text = "Current Trend: ";
    color trend_color = clrGray;

    if(current_trend == 1)
    {
        trend_text += "🟢 BUY";
        trend_color = clrGreen;
    }
    else if(current_trend == -1)
    {
        trend_text += "🔴 SELL";
        trend_color = clrRed;
    }
    else
    {
        trend_text += "🟡 NEUTRAL";
        trend_color = clrOrange;
    }

    ObjectSetString(0, "Dashboard_Trend", OBJPROP_TEXT, trend_text);
    ObjectSetInteger(0, "Dashboard_Trend", OBJPROP_COLOR, trend_color);

    //--- Update trend duration
    string duration_text = "Trend Duration: " + FormatDuration(TimeCurrent() - trend_start_time);
    ObjectSetString(0, "Dashboard_Duration", OBJPROP_TEXT, duration_text);

    //--- Update last signal
    string signal_text = "Last Signal: ";
    if(last_signal_type == 1)
        signal_text += "BUY at " + TimeToString(last_signal_time, TIME_MINUTES);
    else if(last_signal_type == -1)
        signal_text += "SELL at " + TimeToString(last_signal_time, TIME_MINUTES);
    else
        signal_text += "None";

    ObjectSetString(0, "Dashboard_Signal", OBJPROP_TEXT, signal_text);

    //--- Update active trades
    int buy_count = CountPositions(POSITION_TYPE_BUY);
    int sell_count = CountPositions(POSITION_TYPE_SELL);
    string trades_text = StringFormat("Active Trades: %d Buy | %d Sell", buy_count, sell_count);
    ObjectSetString(0, "Dashboard_Trades", OBJPROP_TEXT, trades_text);

    //--- Update P&L
    double buy_profit = CalculateProfit(POSITION_TYPE_BUY);
    double sell_profit = CalculateProfit(POSITION_TYPE_SELL);
    double total_profit = buy_profit + sell_profit;

    string pl_text = StringFormat("P&L: Buy $%.2f | Sell $%.2f", buy_profit, sell_profit);
    ObjectSetString(0, "Dashboard_PL", OBJPROP_TEXT, pl_text);

    string total_pl_text = StringFormat("Total P&L: $%.2f", total_profit);
    color pl_color = (total_profit >= 0) ? clrGreen : clrRed;
    ObjectSetString(0, "Dashboard_Total_PL", OBJPROP_TEXT, total_pl_text);
    ObjectSetInteger(0, "Dashboard_Total_PL", OBJPROP_COLOR, pl_color);
}

//+------------------------------------------------------------------+
//| Delete Dashboard                                                |
//+------------------------------------------------------------------+
void DeleteDashboard()
{
    ObjectDelete(0, "Dashboard_Background");
    ObjectDelete(0, "Dashboard_Title");
    ObjectDelete(0, "Dashboard_Trend");
    ObjectDelete(0, "Dashboard_Duration");
    ObjectDelete(0, "Dashboard_Signal");
    ObjectDelete(0, "Dashboard_Trades");
    ObjectDelete(0, "Dashboard_PL");
    ObjectDelete(0, "Dashboard_Total_PL");
}

//+------------------------------------------------------------------+
//| Format Duration                                                 |
//+------------------------------------------------------------------+
string FormatDuration(int seconds)
{
    int hours = seconds / 3600;
    int minutes = (seconds % 3600) / 60;

    if(hours > 0)
        return StringFormat("%dh %dm", hours, minutes);
    else
        return StringFormat("%dm", minutes);
}

//+------------------------------------------------------------------+
//| Create Scanner                                                  |
//+------------------------------------------------------------------+
void CreateScanner()
{
    if(timeframe_count == 0) return;

    int row_height = 20;
    int header_height = 25;
    int total_height = header_height + (timeframe_count * row_height) + 25;

    //--- Scanner background
    string bg_name = "Scanner_Background";
    if(ObjectCreate(0, bg_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, bg_name, OBJPROP_XDISTANCE, Scanner_X);
        ObjectSetInteger(0, bg_name, OBJPROP_YDISTANCE, Scanner_Y);
        ObjectSetInteger(0, bg_name, OBJPROP_XSIZE, Scanner_Width);
        ObjectSetInteger(0, bg_name, OBJPROP_YSIZE, total_height);
        ObjectSetInteger(0, bg_name, OBJPROP_BGCOLOR, Scanner_Background);
        ObjectSetInteger(0, bg_name, OBJPROP_BORDER_COLOR, clrDarkGray);
        ObjectSetInteger(0, bg_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, bg_name, OBJPROP_BACK, false);
    }

    //--- Scanner title
    string title_name = "Scanner_Title";
    if(ObjectCreate(0, title_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, title_name, OBJPROP_XDISTANCE, Scanner_X + 10);
        ObjectSetInteger(0, title_name, OBJPROP_YDISTANCE, Scanner_Y + 5);
        ObjectSetString(0, title_name, OBJPROP_TEXT, "📈 TIMEFRAME SCANNER - " + Symbol());
        ObjectSetString(0, title_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, title_name, OBJPROP_FONTSIZE, Scanner_Font_Size + 1);
        ObjectSetInteger(0, title_name, OBJPROP_COLOR, Scanner_Header_Color);
        ObjectSetInteger(0, title_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Create header labels
    CreateScannerHeaders();

    //--- Create timeframe rows
    for(int i = 0; i < timeframe_count; i++)
    {
        CreateScannerRow(i);
    }

    //--- Last update label
    string update_name = "Scanner_LastUpdate";
    if(ObjectCreate(0, update_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, update_name, OBJPROP_XDISTANCE, Scanner_X + 10);
        ObjectSetInteger(0, update_name, OBJPROP_YDISTANCE, Scanner_Y + header_height + (timeframe_count * row_height) + 5);
        ObjectSetString(0, update_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, update_name, OBJPROP_FONTSIZE, Scanner_Font_Size - 1);
        ObjectSetInteger(0, update_name, OBJPROP_COLOR, clrGray);
        ObjectSetInteger(0, update_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
}

//+------------------------------------------------------------------+
//| Create Scanner Headers                                          |
//+------------------------------------------------------------------+
void CreateScannerHeaders()
{
    int y_pos = Scanner_Y + 25;

    //--- Timeframe header
    string tf_header = "Scanner_Header_TF";
    if(ObjectCreate(0, tf_header, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, tf_header, OBJPROP_XDISTANCE, Scanner_X + 20);
        ObjectSetInteger(0, tf_header, OBJPROP_YDISTANCE, y_pos);
        ObjectSetString(0, tf_header, OBJPROP_TEXT, "Timeframe");
        ObjectSetString(0, tf_header, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, tf_header, OBJPROP_FONTSIZE, Scanner_Font_Size);
        ObjectSetInteger(0, tf_header, OBJPROP_COLOR, Scanner_Header_Color);
        ObjectSetInteger(0, tf_header, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Status header
    string status_header = "Scanner_Header_Status";
    if(ObjectCreate(0, status_header, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, status_header, OBJPROP_XDISTANCE, Scanner_X + 120);
        ObjectSetInteger(0, status_header, OBJPROP_YDISTANCE, y_pos);
        ObjectSetString(0, status_header, OBJPROP_TEXT, "Status");
        ObjectSetString(0, status_header, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, status_header, OBJPROP_FONTSIZE, Scanner_Font_Size);
        ObjectSetInteger(0, status_header, OBJPROP_COLOR, Scanner_Header_Color);
        ObjectSetInteger(0, status_header, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Signal header
    string signal_header = "Scanner_Header_Signal";
    if(ObjectCreate(0, signal_header, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, signal_header, OBJPROP_XDISTANCE, Scanner_X + 200);
        ObjectSetInteger(0, signal_header, OBJPROP_YDISTANCE, y_pos);
        ObjectSetString(0, signal_header, OBJPROP_TEXT, "Signal");
        ObjectSetString(0, signal_header, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, signal_header, OBJPROP_FONTSIZE, Scanner_Font_Size);
        ObjectSetInteger(0, signal_header, OBJPROP_COLOR, Scanner_Header_Color);
        ObjectSetInteger(0, signal_header, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Last Change header
    string change_header = "Scanner_Header_Change";
    if(ObjectCreate(0, change_header, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, change_header, OBJPROP_XDISTANCE, Scanner_X + 280);
        ObjectSetInteger(0, change_header, OBJPROP_YDISTANCE, y_pos);
        ObjectSetString(0, change_header, OBJPROP_TEXT, "Last Change");
        ObjectSetString(0, change_header, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, change_header, OBJPROP_FONTSIZE, Scanner_Font_Size);
        ObjectSetInteger(0, change_header, OBJPROP_COLOR, Scanner_Header_Color);
        ObjectSetInteger(0, change_header, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
}

//+------------------------------------------------------------------+
//| Create Scanner Row                                              |
//+------------------------------------------------------------------+
void CreateScannerRow(int row)
{
    int y_pos = Scanner_Y + 50 + (row * 20);

    //--- Timeframe label
    string tf_name = "Scanner_TF_" + IntegerToString(row);
    if(ObjectCreate(0, tf_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, tf_name, OBJPROP_XDISTANCE, Scanner_X + 20);
        ObjectSetInteger(0, tf_name, OBJPROP_YDISTANCE, y_pos);
        ObjectSetString(0, tf_name, OBJPROP_TEXT, timeframe_list[row]);
        ObjectSetString(0, tf_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, tf_name, OBJPROP_FONTSIZE, Scanner_Font_Size);
        ObjectSetInteger(0, tf_name, OBJPROP_COLOR, clrBlack);
        ObjectSetInteger(0, tf_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Status label
    string status_name = "Scanner_Status_" + IntegerToString(row);
    if(ObjectCreate(0, status_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, status_name, OBJPROP_XDISTANCE, Scanner_X + 120);
        ObjectSetInteger(0, status_name, OBJPROP_YDISTANCE, y_pos);
        ObjectSetString(0, status_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, status_name, OBJPROP_FONTSIZE, Scanner_Font_Size);
        ObjectSetInteger(0, status_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Signal label
    string signal_name = "Scanner_Signal_" + IntegerToString(row);
    if(ObjectCreate(0, signal_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, signal_name, OBJPROP_XDISTANCE, Scanner_X + 200);
        ObjectSetInteger(0, signal_name, OBJPROP_YDISTANCE, y_pos);
        ObjectSetString(0, signal_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, signal_name, OBJPROP_FONTSIZE, Scanner_Font_Size);
        ObjectSetInteger(0, signal_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Last change label
    string change_name = "Scanner_Change_" + IntegerToString(row);
    if(ObjectCreate(0, change_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, change_name, OBJPROP_XDISTANCE, Scanner_X + 280);
        ObjectSetInteger(0, change_name, OBJPROP_YDISTANCE, y_pos);
        ObjectSetString(0, change_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, change_name, OBJPROP_FONTSIZE, Scanner_Font_Size);
        ObjectSetInteger(0, change_name, OBJPROP_COLOR, clrGray);
        ObjectSetInteger(0, change_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }
}

//+------------------------------------------------------------------+
//| Update Scanner                                                  |
//+------------------------------------------------------------------+
void UpdateScanner()
{
    for(int i = 0; i < timeframe_count; i++)
    {
        int trend = GetSupertrendTrend(timeframe_enums[i]);

        string status_text = "";
        string signal_text = "";
        color status_color = clrGray;

        if(trend == 1)
        {
            status_text = "🟢 BUY";
            signal_text = "↗️";
            status_color = clrGreen;
        }
        else if(trend == -1)
        {
            status_text = "🔴 SELL";
            signal_text = "↘️";
            status_color = clrRed;
        }
        else
        {
            status_text = "🟡 NEUTRAL";
            signal_text = "➡️";
            status_color = clrOrange;
        }

        //--- Update status
        string status_name = "Scanner_Status_" + IntegerToString(i);
        ObjectSetString(0, status_name, OBJPROP_TEXT, status_text);
        ObjectSetInteger(0, status_name, OBJPROP_COLOR, status_color);

        //--- Update signal
        string signal_name = "Scanner_Signal_" + IntegerToString(i);
        ObjectSetString(0, signal_name, OBJPROP_TEXT, signal_text);
        ObjectSetInteger(0, signal_name, OBJPROP_COLOR, status_color);

        //--- Update last change (simplified for now)
        string change_name = "Scanner_Change_" + IntegerToString(i);
        ObjectSetString(0, change_name, OBJPROP_TEXT, "Recent");
    }

    //--- Update last update time
    string update_text = "Last Update: " + TimeToString(TimeCurrent(), TIME_SECONDS);
    ObjectSetString(0, "Scanner_LastUpdate", OBJPROP_TEXT, update_text);
}

//+------------------------------------------------------------------+
//| Get Supertrend Trend for Timeframe                             |
//+------------------------------------------------------------------+
int GetSupertrendTrend(ENUM_TIMEFRAMES tf)
{
    //--- Get ATR for this timeframe
    int tf_atr_handle = iATR(Symbol(), tf, ATR_Period);
    if(tf_atr_handle == INVALID_HANDLE) return 0;

    double tf_atr_values[];
    ArraySetAsSeries(tf_atr_values, true);

    if(CopyBuffer(tf_atr_handle, 0, 0, 3, tf_atr_values) < 3)
    {
        IndicatorRelease(tf_atr_handle);
        return 0;
    }

    //--- Get price data for this timeframe
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    if(CopyHigh(Symbol(), tf, 0, 3, high) < 3 ||
       CopyLow(Symbol(), tf, 0, 3, low) < 3 ||
       CopyClose(Symbol(), tf, 0, 3, close) < 3)
    {
        IndicatorRelease(tf_atr_handle);
        return 0;
    }

    //--- Calculate source price based on input parameter
    double src = 0;
    switch(Source_Price)
    {
        case PRICE_CLOSE: src = close[0]; break;
        case PRICE_OPEN: src = iOpen(Symbol(), tf, 0); break;
        case PRICE_HIGH: src = high[0]; break;
        case PRICE_LOW: src = low[0]; break;
        case PRICE_MEDIAN: src = (high[0] + low[0]) / 2.0; break;
        case PRICE_TYPICAL: src = (high[0] + low[0] + close[0]) / 3.0; break;
        case PRICE_WEIGHTED: src = (high[0] + low[0] + 2 * close[0]) / 4.0; break;
        default: src = (high[0] + low[0]) / 2.0; break;
    }

    //--- Calculate Supertrend for this timeframe
    double atr = tf_atr_values[0];
    double up = src - (ATR_Multiplier * atr);
    double dn = src + (ATR_Multiplier * atr);

    //--- Use static variables for each timeframe (simplified approach)
    static double tf_prev_up[], tf_prev_dn[];
    static int tf_prev_trend[];
    static bool tf_initialized[];

    //--- Get timeframe index for static arrays
    int tf_index = GetTimeframeIndex(tf);
    if(tf_index < 0)
    {
        IndicatorRelease(tf_atr_handle);
        return 0;
    }

    //--- Initialize arrays if needed
    if(ArraySize(tf_prev_up) <= tf_index)
    {
        ArrayResize(tf_prev_up, tf_index + 1);
        ArrayResize(tf_prev_dn, tf_index + 1);
        ArrayResize(tf_prev_trend, tf_index + 1);
        ArrayResize(tf_initialized, tf_index + 1);
    }

    //--- Initialize values for this timeframe
    if(!tf_initialized[tf_index])
    {
        tf_prev_up[tf_index] = up;
        tf_prev_dn[tf_index] = dn;
        tf_prev_trend[tf_index] = 1;
        tf_initialized[tf_index] = true;
    }

    //--- Apply Supertrend logic
    up = (close[1] > tf_prev_up[tf_index]) ? MathMax(up, tf_prev_up[tf_index]) : up;
    dn = (close[1] < tf_prev_dn[tf_index]) ? MathMin(dn, tf_prev_dn[tf_index]) : dn;

    int trend = tf_prev_trend[tf_index];
    if(trend == -1 && close[0] > tf_prev_dn[tf_index])
        trend = 1;
    else if(trend == 1 && close[0] < tf_prev_up[tf_index])
        trend = -1;

    //--- Update values
    tf_prev_up[tf_index] = up;
    tf_prev_dn[tf_index] = dn;
    tf_prev_trend[tf_index] = trend;

    IndicatorRelease(tf_atr_handle);
    return trend;
}

//+------------------------------------------------------------------+
//| Get Timeframe Index for Static Arrays                          |
//+------------------------------------------------------------------+
int GetTimeframeIndex(ENUM_TIMEFRAMES tf)
{
    switch(tf)
    {
        case PERIOD_M1: return 0;
        case PERIOD_M5: return 1;
        case PERIOD_M15: return 2;
        case PERIOD_M30: return 3;
        case PERIOD_H1: return 4;
        case PERIOD_H4: return 5;
        case PERIOD_D1: return 6;
        case PERIOD_W1: return 7;
        case PERIOD_MN1: return 8;
        default: return -1;
    }
}

//+------------------------------------------------------------------+
//| Delete Scanner                                                  |
//+------------------------------------------------------------------+
void DeleteScanner()
{
    ObjectDelete(0, "Scanner_Background");
    ObjectDelete(0, "Scanner_Title");
    ObjectDelete(0, "Scanner_Header_TF");
    ObjectDelete(0, "Scanner_Header_Status");
    ObjectDelete(0, "Scanner_Header_Signal");
    ObjectDelete(0, "Scanner_Header_Change");
    ObjectDelete(0, "Scanner_LastUpdate");

    //--- Delete timeframe rows
    for(int i = 0; i < timeframe_count; i++)
    {
        ObjectDelete(0, "Scanner_TF_" + IntegerToString(i));
        ObjectDelete(0, "Scanner_Status_" + IntegerToString(i));
        ObjectDelete(0, "Scanner_Signal_" + IntegerToString(i));
        ObjectDelete(0, "Scanner_Change_" + IntegerToString(i));
    }
}

//+------------------------------------------------------------------+
//| Draw Trend Lines                                               |
//+------------------------------------------------------------------+
void DrawTrendLines(double up_value, double dn_value)
{
    if(!Show_Trend_Lines) return;

    datetime time_current = iTime(Symbol(), PERIOD_CURRENT, 0);
    datetime time_prev = iTime(Symbol(), PERIOD_CURRENT, 1);

    //--- Draw upper trend line (bullish)
    if(current_trend == 1)
    {
        string up_line_name = "Supertrend_Up_" + IntegerToString(time_current);
        if(ObjectCreate(0, up_line_name, OBJ_TREND, 0, time_prev, up_value, time_current, up_value))
        {
            ObjectSetInteger(0, up_line_name, OBJPROP_COLOR, clrGreen);
            ObjectSetInteger(0, up_line_name, OBJPROP_WIDTH, Trend_Line_Width);
            ObjectSetInteger(0, up_line_name, OBJPROP_RAY_RIGHT, false);
        }
    }

    //--- Draw lower trend line (bearish)
    if(current_trend == -1)
    {
        string dn_line_name = "Supertrend_Dn_" + IntegerToString(time_current);
        if(ObjectCreate(0, dn_line_name, OBJ_TREND, 0, time_prev, dn_value, time_current, dn_value))
        {
            ObjectSetInteger(0, dn_line_name, OBJPROP_COLOR, clrRed);
            ObjectSetInteger(0, dn_line_name, OBJPROP_WIDTH, Trend_Line_Width);
            ObjectSetInteger(0, dn_line_name, OBJPROP_RAY_RIGHT, false);
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Signals                                                   |
//+------------------------------------------------------------------+
void DrawSignals()
{
    if(!Show_Buy_Sell_Signals) return;

    datetime time_current = iTime(Symbol(), PERIOD_CURRENT, 0);

    //--- Draw buy signal
    if(CheckBuySignal())
    {
        double low_price = iLow(Symbol(), PERIOD_CURRENT, 0);
        string buy_arrow_name = "Buy_Signal_" + IntegerToString(time_current);

        if(ObjectCreate(0, buy_arrow_name, OBJ_ARROW_BUY, 0, time_current, low_price))
        {
            ObjectSetInteger(0, buy_arrow_name, OBJPROP_COLOR, Buy_Signal_Color);
            ObjectSetInteger(0, buy_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        }
    }

    //--- Draw sell signal
    if(CheckSellSignal())
    {
        double high_price = iHigh(Symbol(), PERIOD_CURRENT, 0);
        string sell_arrow_name = "Sell_Signal_" + IntegerToString(time_current);

        if(ObjectCreate(0, sell_arrow_name, OBJ_ARROW_SELL, 0, time_current, high_price))
        {
            ObjectSetInteger(0, sell_arrow_name, OBJPROP_COLOR, Sell_Signal_Color);
            ObjectSetInteger(0, sell_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        }
    }
}
