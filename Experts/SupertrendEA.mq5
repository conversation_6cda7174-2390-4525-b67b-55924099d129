//+------------------------------------------------------------------+
//|                                                SupertrendEA.mq5 |
//|                                  Copyright 2024, Supertrend EA  |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Supertrend EA"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "Supertrend Expert Advisor with Dashboard and Multi-Timeframe Scanner"

//--- Include files
#include <Trade\Trade.mqh>
#include <Controls\Dialog.mqh>
#include <Controls\Label.mqh>
#include <Controls\Panel.mqh>

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+

//--- Supertrend Settings
input group "📊 SUPERTREND SETTINGS"
input int                ATR_Period = 10;                    // ATR Period
input double             ATR_Multiplier = 3.0;              // ATR Multiplier
input bool               Use_Smoothed_ATR = true;           // Use Smoothed ATR (true=ATR, false=SMA)
input ENUM_APPLIED_PRICE Source_Price = PRICE_MEDIAN;       // Source Price (HL2)

//--- Trading Settings
input group "💰 TRADING SETTINGS"
input double             Lot_Size = 0.1;                    // Lot Size
input int                Take_Profit = 500;                 // Take Profit (points)
input int                Stop_Loss = 300;                   // Stop Loss (points)
input bool               Enable_Trailing_Stop = false;      // Enable Trailing Stop
input int                Trailing_Stop_Distance = 200;      // Trailing Stop Distance (points)
input int                Trailing_Stop_Step = 50;           // Trailing Stop Step (points)
input int                Magic_Number = 12345;              // Magic Number
input string             Trade_Comment = "Supertrend EA";   // Trade Comment
input bool               Enable_Auto_Trading = true;        // Enable Auto Trading
input bool               Allow_Hedging = true;              // Allow Multiple Positions

//--- Dashboard Settings
input group "📋 DASHBOARD SETTINGS"
input bool               Show_Dashboard = true;             // Show Dashboard
input int                Dashboard_X = 20;                  // Dashboard X Position
input int                Dashboard_Y = 50;                  // Dashboard Y Position
input int                Dashboard_Width = 280;             // Dashboard Width
input int                Dashboard_Height = 200;            // Dashboard Height
input color              Dashboard_Background = C'240,240,240'; // Dashboard Background Color
input color              Dashboard_Border = clrDarkGray;    // Dashboard Border Color
input int                Dashboard_Font_Size = 9;           // Dashboard Font Size

//--- Scanner Settings
input group "🔍 MULTI-TIMEFRAME SCANNER SETTINGS"
input bool               Enable_Scanner = true;             // Enable Scanner
input string             Scanner_Timeframes = "M1,M5,M15,M20,M30,H1,H2,H3,H4,H8,H12,D1,W1"; // Timeframes (comma separated)
input int                Scanner_X = 632;                   // Scanner X Position (from right)
input int                Scanner_Y = 40;                    // Scanner Y Position
input int                Scanner_Width = 617;               // Scanner Width
input int                Scanner_Height = 374;              // Scanner Height
input int                Scanner_Update_Seconds = 5;        // Update Frequency (seconds)
input color              Scanner_Background = C'30,30,30';  // Scanner Background Color
input color              Scanner_Header_Color = C'60,60,60'; // Scanner Header Color
input int                Scanner_Font_Size = 11;            // Scanner Font Size

//--- Chart Visualization
input group "🎨 CHART VISUALIZATION"
input bool               Show_Buy_Sell_Signals = true;      // Show Buy/Sell Signals
input bool               Show_Signal_Labels = true;         // Show BUY/SELL Labels
input bool               Show_Signal_Background = true;     // Show Signal Background
input bool               Show_Historical_Signals = true;    // Show Historical Signals on Startup
input int                Historical_Bars = 500;             // Historical Bars to Analyze (50-1000)
input bool               Show_Trend_Lines = true;           // Show Supertrend Lines
input bool               Highlight_Trend_Zones = true;      // Highlight Trend Zones
input int                Signal_Arrow_Size = 3;             // Signal Arrow Size (1-5)
input int                Signal_Label_Size = 8;             // Signal Label Font Size
input int                Trend_Line_Width = 2;              // Trend Line Width
input color              Buy_Signal_Color = clrLime;        // Buy Signal Color
input color              Sell_Signal_Color = clrRed;        // Sell Signal Color
input color              Bullish_Zone_Color = C'230,255,230'; // Bullish Zone Color
input color              Bearish_Zone_Color = C'255,230,230'; // Bearish Zone Color
input bool               Show_Heiken_Ashi = false;          // Show Heiken Ashi Overlay

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
CTrade trade;
int atr_handle;

//--- Supertrend calculation arrays
double supertrend_up[], supertrend_down[], trend_buffer[];
double atr_buffer[];
double up_trend[], down_trend[];

//--- Current state variables
int current_trend = 0;
int previous_trend = 0;
datetime trend_start_time = 0;
datetime last_signal_time = 0;
int last_signal_type = 0; // 1 = Buy, -1 = Sell

//--- Previous values for Supertrend calculation
double prev_up = 0;
double prev_down = 0;

//--- Dashboard objects
string dashboard_objects[];
string scanner_objects[];

//--- Scanner variables
string timeframe_list[];
ENUM_TIMEFRAMES timeframe_enums[];
int timeframe_count = 0;
bool scanner_panel_visible = true;

//--- Scanner indicator values
double scanner_rsi_values[];
double scanner_stoch_values[];
double scanner_cci_values[];
double scanner_adx_values[];
double scanner_ao_values[];

//--- Scanner UI constants
#define SCANNER_MAIN_PANEL          "SCANNER_PANEL_MAIN"
#define SCANNER_HEADER_PANEL        "SCANNER_PANEL_HEADER"
#define SCANNER_HEADER_ICON         "SCANNER_PANEL_HEADER_ICON"
#define SCANNER_HEADER_TEXT         "SCANNER_PANEL_HEADER_TEXT"
#define SCANNER_CLOSE_BUTTON        "SCANNER_BUTTON_CLOSE"
#define SCANNER_SYMBOL_RECTANGLE    "SCANNER_SYMBOL_HEADER"
#define SCANNER_SYMBOL_TEXT         "SCANNER_SYMBOL_TEXT"
#define SCANNER_TIMEFRAME_RECTANGLE "SCANNER_TIMEFRAME_"
#define SCANNER_TIMEFRAME_TEXT      "SCANNER_TIMEFRAME_TEXT_"
#define SCANNER_HEADER_RECTANGLE    "SCANNER_HEADER_"
#define SCANNER_HEADER_TEXT         "SCANNER_HEADER_TEXT_"
#define SCANNER_RSI_RECTANGLE       "SCANNER_RSI_"
#define SCANNER_RSI_TEXT            "SCANNER_RSI_TEXT_"
#define SCANNER_STOCH_RECTANGLE     "SCANNER_STOCH_"
#define SCANNER_STOCH_TEXT          "SCANNER_STOCH_TEXT_"
#define SCANNER_CCI_RECTANGLE       "SCANNER_CCI_"
#define SCANNER_CCI_TEXT            "SCANNER_CCI_TEXT_"
#define SCANNER_ADX_RECTANGLE       "SCANNER_ADX_"
#define SCANNER_ADX_TEXT            "SCANNER_ADX_TEXT_"
#define SCANNER_AO_RECTANGLE        "SCANNER_AO_"
#define SCANNER_AO_TEXT             "SCANNER_AO_TEXT_"
#define SCANNER_BUY_RECTANGLE       "SCANNER_BUY_"
#define SCANNER_BUY_TEXT            "SCANNER_BUY_TEXT_"
#define SCANNER_SELL_RECTANGLE      "SCANNER_SELL_"
#define SCANNER_SELL_TEXT           "SCANNER_SELL_TEXT_"

//--- Scanner dimensions
#define SCANNER_WIDTH_TIMEFRAME     90
#define SCANNER_WIDTH_INDICATOR     70
#define SCANNER_WIDTH_SIGNAL        90
#define SCANNER_HEIGHT_RECTANGLE    25

//--- Timer variables
datetime last_update_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Initialize trade object
    trade.SetExpertMagicNumber(Magic_Number);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(Symbol());
    
    //--- Create ATR indicator for Supertrend calculation
    if(!InitializeSupertrendCalculation())
    {
        Print("Failed to initialize Supertrend calculation");
        return INIT_FAILED;
    }
    
    //--- Parse timeframes for scanner
    if(Enable_Scanner)
    {
        ParseTimeframes();
        InitializeScannerArrays();
    }
    
    //--- Create dashboard
    if(Show_Dashboard)
    {
        CreateDashboard();
    }
    
    //--- Create scanner
    if(Enable_Scanner && scanner_panel_visible)
    {
        CreateScanner();
    }
    
    //--- Set timer for updates
    EventSetTimer(Scanner_Update_Seconds);
    
    //--- Initialize trend tracking
    trend_start_time = TimeCurrent();

    //--- Clean up any existing signal objects
    CleanupSignalObjects();

    //--- Calculate and display historical signals
    if(Show_Buy_Sell_Signals && Show_Historical_Signals)
    {
        CalculateHistoricalSignals();
    }

    Print("Supertrend EA initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Kill timer
    EventKillTimer();

    //--- Clean up dashboard objects
    DeleteDashboard();

    //--- Clean up scanner objects
    DeleteScanner();

    //--- Clean up signal objects
    CleanupSignalObjects();

    //--- Release ATR indicator handle
    if(atr_handle != INVALID_HANDLE)
        IndicatorRelease(atr_handle);

    Print("Supertrend EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                            |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check if auto trading is enabled
    if(!Enable_Auto_Trading)
        return;
    
    //--- Calculate Supertrend values
    if(!CalculateSupertrendValues())
        return;
    
    //--- Check for trend changes and signals
    CheckTrendChange();
    
    //--- Check for trading signals
    if(CheckBuySignal())
    {
        if(Allow_Hedging || CountPositions(POSITION_TYPE_BUY) == 0)
        {
            OpenBuyOrder();
        }
    }
    else if(CheckSellSignal())
    {
        if(Allow_Hedging || CountPositions(POSITION_TYPE_SELL) == 0)
        {
            OpenSellOrder();
        }
    }
    
    //--- Manage existing trades
    if(Enable_Trailing_Stop)
    {
        ManageTrailingStop();
    }
    
    //--- Update dashboard on new bar
    static datetime last_bar_time = 0;
    datetime current_bar_time = iTime(Symbol(), PERIOD_CURRENT, 0);
    if((int)current_bar_time != (int)last_bar_time)
    {
        if(Show_Dashboard)
            UpdateDashboard();
        last_bar_time = current_bar_time;
    }
}

//+------------------------------------------------------------------+
//| Timer function                                                  |
//+------------------------------------------------------------------+
void OnTimer()
{
    //--- Update scanner
    if(Enable_Scanner && scanner_panel_visible)
    {
        UpdateScanner();
    }

    //--- Update dashboard
    if(Show_Dashboard)
    {
        UpdateDashboard();
    }
}

//+------------------------------------------------------------------+
//| Initialize Supertrend Calculation                               |
//+------------------------------------------------------------------+
bool InitializeSupertrendCalculation()
{
    //--- Create ATR indicator
    atr_handle = iATR(Symbol(), PERIOD_CURRENT, ATR_Period);

    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator");
        return false;
    }

    //--- Initialize arrays
    ArraySetAsSeries(supertrend_up, true);
    ArraySetAsSeries(supertrend_down, true);
    ArraySetAsSeries(trend_buffer, true);
    ArraySetAsSeries(atr_buffer, true);
    ArraySetAsSeries(up_trend, true);
    ArraySetAsSeries(down_trend, true);

    //--- Resize arrays
    ArrayResize(supertrend_up, 1000);
    ArrayResize(supertrend_down, 1000);
    ArrayResize(trend_buffer, 1000);
    ArrayResize(atr_buffer, 1000);
    ArrayResize(up_trend, 1000);
    ArrayResize(down_trend, 1000);

    //--- Initialize values
    prev_up = 0;
    prev_down = 0;
    current_trend = 1; // Start with bullish trend

    return true;
}

//+------------------------------------------------------------------+
//| Calculate Supertrend Values                                     |
//+------------------------------------------------------------------+
bool CalculateSupertrendValues()
{
    //--- Get ATR values
    if(CopyBuffer(atr_handle, 0, 0, 3, atr_buffer) < 3)
    {
        Print("Failed to copy ATR values");
        return false;
    }

    //--- Get price data
    double high[], low[], close[];
    ArraySetAsSeries(high, true);
    ArraySetAsSeries(low, true);
    ArraySetAsSeries(close, true);

    if(CopyHigh(Symbol(), PERIOD_CURRENT, 0, 3, high) < 3 ||
       CopyLow(Symbol(), PERIOD_CURRENT, 0, 3, low) < 3 ||
       CopyClose(Symbol(), PERIOD_CURRENT, 0, 3, close) < 3)
    {
        Print("Failed to copy price data");
        return false;
    }

    //--- Calculate source price based on input parameter
    double src = 0;
    switch(Source_Price)
    {
        case PRICE_CLOSE: src = close[0]; break;
        case PRICE_OPEN: src = iOpen(Symbol(), PERIOD_CURRENT, 0); break;
        case PRICE_HIGH: src = high[0]; break;
        case PRICE_LOW: src = low[0]; break;
        case PRICE_MEDIAN: src = (high[0] + low[0]) / 2.0; break;
        case PRICE_TYPICAL: src = (high[0] + low[0] + close[0]) / 3.0; break;
        case PRICE_WEIGHTED: src = (high[0] + low[0] + 2 * close[0]) / 4.0; break;
        default: src = (high[0] + low[0]) / 2.0; break;
    }

    //--- Get ATR value
    double atr = atr_buffer[0];

    //--- Calculate basic upper and lower bands
    double up = src - (ATR_Multiplier * atr);
    double dn = src + (ATR_Multiplier * atr);

    //--- Apply Supertrend logic
    if(prev_up == 0) prev_up = up;
    if(prev_down == 0) prev_down = dn;

    up = (close[1] > prev_up) ? MathMax(up, prev_up) : up;
    dn = (close[1] < prev_down) ? MathMin(dn, prev_down) : dn;

    //--- Store current values
    up_trend[0] = up;
    down_trend[0] = dn;

    //--- Determine trend
    previous_trend = current_trend;

    if(current_trend == -1 && close[0] > prev_down)
        current_trend = 1;
    else if(current_trend == 1 && close[0] < prev_up)
        current_trend = -1;

    //--- Store trend
    trend_buffer[0] = current_trend;

    //--- Set Supertrend values based on trend
    if(current_trend == 1)
    {
        supertrend_up[0] = up;
        supertrend_down[0] = EMPTY_VALUE;
    }
    else
    {
        supertrend_up[0] = EMPTY_VALUE;
        supertrend_down[0] = dn;
    }

    //--- Update previous values for next calculation
    prev_up = up;
    prev_down = dn;

    //--- Draw trend lines if enabled
    if(Show_Trend_Lines)
    {
        DrawTrendLines(up, dn);
    }

    //--- Draw signals if enabled
    if(Show_Buy_Sell_Signals)
    {
        DrawSignals();
    }

    return true;
}

//+------------------------------------------------------------------+
//| Check for trend change                                          |
//+------------------------------------------------------------------+
void CheckTrendChange()
{
    if(current_trend != previous_trend && previous_trend != 0)
    {
        trend_start_time = TimeCurrent();
        last_signal_time = TimeCurrent();
        last_signal_type = current_trend;

        Print("Trend changed from ", previous_trend, " to ", current_trend);
    }
}

//+------------------------------------------------------------------+
//| Check Buy Signal                                                |
//+------------------------------------------------------------------+
bool CheckBuySignal()
{
    return (current_trend == 1 && previous_trend == -1);
}

//+------------------------------------------------------------------+
//| Check Sell Signal                                               |
//+------------------------------------------------------------------+
bool CheckSellSignal()
{
    return (current_trend == -1 && previous_trend == 1);
}

//+------------------------------------------------------------------+
//| Open Buy Order                                                  |
//+------------------------------------------------------------------+
void OpenBuyOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double sl = (Stop_Loss > 0) ? price - (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price + (Take_Profit * Point()) : 0;

    if(trade.Buy(Lot_Size, Symbol(), price, sl, tp, Trade_Comment))
    {
        Print("Buy order opened at ", price);
    }
    else
    {
        Print("Failed to open buy order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Open Sell Order                                                 |
//+------------------------------------------------------------------+
void OpenSellOrder()
{
    double price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double sl = (Stop_Loss > 0) ? price + (Stop_Loss * Point()) : 0;
    double tp = (Take_Profit > 0) ? price - (Take_Profit * Point()) : 0;

    if(trade.Sell(Lot_Size, Symbol(), price, sl, tp, Trade_Comment))
    {
        Print("Sell order opened at ", price);
    }
    else
    {
        Print("Failed to open sell order. Error: ", GetLastError());
    }
}

//+------------------------------------------------------------------+
//| Count Positions                                                 |
//+------------------------------------------------------------------+
int CountPositions(ENUM_POSITION_TYPE type)
{
    int count = 0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == type)
        {
            count++;
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Calculate Profit for Position Type                              |
//+------------------------------------------------------------------+
double CalculateProfit(ENUM_POSITION_TYPE type)
{
    double profit = 0.0;
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number &&
           PositionGetInteger(POSITION_TYPE) == type)
        {
            profit += PositionGetDouble(POSITION_PROFIT);
        }
    }
    return profit;
}

//+------------------------------------------------------------------+
//| Manage Trailing Stop                                            |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == Symbol() &&
           PositionGetInteger(POSITION_MAGIC) == Magic_Number)
        {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            ENUM_POSITION_TYPE pos_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            double open_price = PositionGetDouble(POSITION_PRICE_OPEN);
            double current_sl = PositionGetDouble(POSITION_SL);
            double current_price = (pos_type == POSITION_TYPE_BUY) ?
                                   SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                                   SymbolInfoDouble(Symbol(), SYMBOL_ASK);

            double new_sl = 0;
            bool modify = false;

            if(pos_type == POSITION_TYPE_BUY)
            {
                new_sl = current_price - (Trailing_Stop_Distance * Point());
                if(new_sl > current_sl + (Trailing_Stop_Step * Point()) || current_sl == 0)
                {
                    modify = true;
                }
            }
            else if(pos_type == POSITION_TYPE_SELL)
            {
                new_sl = current_price + (Trailing_Stop_Distance * Point());
                if(new_sl < current_sl - (Trailing_Stop_Step * Point()) || current_sl == 0)
                {
                    modify = true;
                }
            }

            if(modify)
            {
                double tp = PositionGetDouble(POSITION_TP);
                if(trade.PositionModify(ticket, new_sl, tp))
                {
                    Print("Trailing stop updated for position ", ticket, " New SL: ", new_sl);
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Initialize Scanner Arrays                                        |
//+------------------------------------------------------------------+
void InitializeScannerArrays()
{
    ArraySetAsSeries(scanner_rsi_values, true);
    ArraySetAsSeries(scanner_stoch_values, true);
    ArraySetAsSeries(scanner_cci_values, true);
    ArraySetAsSeries(scanner_adx_values, true);
    ArraySetAsSeries(scanner_ao_values, true);
}

//+------------------------------------------------------------------+
//| Parse Timeframes from Input String                              |
//+------------------------------------------------------------------+
void ParseTimeframes()
{
    string tf_string = Scanner_Timeframes;
    string separator = ",";

    //--- Clear arrays
    ArrayFree(timeframe_list);
    ArrayFree(timeframe_enums);

    //--- Split string
    string parts[];
    int count = StringSplit(tf_string, StringGetCharacter(separator, 0), parts);

    ArrayResize(timeframe_list, count);
    ArrayResize(timeframe_enums, count);

    for(int i = 0; i < count; i++)
    {
        StringTrimLeft(parts[i]);
        StringTrimRight(parts[i]);
        timeframe_list[i] = parts[i];
        timeframe_enums[i] = StringToTimeframe(parts[i]);
    }

    timeframe_count = count;
}

//+------------------------------------------------------------------+
//| Truncate Timeframe Name for Display                             |
//+------------------------------------------------------------------+
string TruncateTimeframeName(int timeframe_index)
{
    if(timeframe_index >= 0 && timeframe_index < timeframe_count)
    {
        string timeframe_string = StringSubstr(EnumToString(timeframe_enums[timeframe_index]), 7);
        return timeframe_string;
    }
    return "";
}

//+------------------------------------------------------------------+
//| Convert String to Timeframe                                     |
//+------------------------------------------------------------------+
ENUM_TIMEFRAMES StringToTimeframe(string tf_str)
{
    if(tf_str == "M1") return PERIOD_M1;
    if(tf_str == "M5") return PERIOD_M5;
    if(tf_str == "M15") return PERIOD_M15;
    if(tf_str == "M20") return PERIOD_M20;
    if(tf_str == "M30") return PERIOD_M30;
    if(tf_str == "H1") return PERIOD_H1;
    if(tf_str == "H2") return PERIOD_H2;
    if(tf_str == "H3") return PERIOD_H3;
    if(tf_str == "H4") return PERIOD_H4;
    if(tf_str == "H8") return PERIOD_H8;
    if(tf_str == "H12") return PERIOD_H12;
    if(tf_str == "D1") return PERIOD_D1;
    if(tf_str == "W1") return PERIOD_W1;
    if(tf_str == "MN1") return PERIOD_MN1;

    return PERIOD_CURRENT;
}

//+------------------------------------------------------------------+
//| Create Dashboard                                                |
//+------------------------------------------------------------------+
void CreateDashboard()
{
    //--- Dashboard background
    string bg_name = "Dashboard_Background";
    if(ObjectCreate(0, bg_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, bg_name, OBJPROP_XDISTANCE, Dashboard_X);
        ObjectSetInteger(0, bg_name, OBJPROP_YDISTANCE, Dashboard_Y);
        ObjectSetInteger(0, bg_name, OBJPROP_XSIZE, Dashboard_Width);
        ObjectSetInteger(0, bg_name, OBJPROP_YSIZE, Dashboard_Height);
        ObjectSetInteger(0, bg_name, OBJPROP_BGCOLOR, Dashboard_Background);
        ObjectSetInteger(0, bg_name, OBJPROP_BORDER_COLOR, Dashboard_Border);
        ObjectSetInteger(0, bg_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, bg_name, OBJPROP_BACK, false);
    }

    //--- Dashboard title
    string title_name = "Dashboard_Title";
    if(ObjectCreate(0, title_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, title_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, title_name, OBJPROP_YDISTANCE, Dashboard_Y + 10);
        ObjectSetString(0, title_name, OBJPROP_TEXT, "📊 SUPERTREND DASHBOARD");
        ObjectSetString(0, title_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, title_name, OBJPROP_FONTSIZE, Dashboard_Font_Size + 1);
        ObjectSetInteger(0, title_name, OBJPROP_COLOR, clrNavy);
        ObjectSetInteger(0, title_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Current trend label
    string trend_name = "Dashboard_Trend";
    if(ObjectCreate(0, trend_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, trend_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, trend_name, OBJPROP_YDISTANCE, Dashboard_Y + 35);
        ObjectSetString(0, trend_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, trend_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, trend_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Trend duration label
    string duration_name = "Dashboard_Duration";
    if(ObjectCreate(0, duration_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, duration_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, duration_name, OBJPROP_YDISTANCE, Dashboard_Y + 55);
        ObjectSetString(0, duration_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, duration_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, duration_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, duration_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Last signal label
    string signal_name = "Dashboard_Signal";
    if(ObjectCreate(0, signal_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, signal_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, signal_name, OBJPROP_YDISTANCE, Dashboard_Y + 75);
        ObjectSetString(0, signal_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, signal_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, signal_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, signal_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Active trades label
    string trades_name = "Dashboard_Trades";
    if(ObjectCreate(0, trades_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, trades_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, trades_name, OBJPROP_YDISTANCE, Dashboard_Y + 95);
        ObjectSetString(0, trades_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, trades_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, trades_name, OBJPROP_COLOR, clrDarkBlue);
        ObjectSetInteger(0, trades_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- P&L label
    string pl_name = "Dashboard_PL";
    if(ObjectCreate(0, pl_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, pl_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, pl_name, OBJPROP_YDISTANCE, Dashboard_Y + 115);
        ObjectSetString(0, pl_name, OBJPROP_FONT, "Arial");
        ObjectSetInteger(0, pl_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, pl_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Total P&L label
    string total_pl_name = "Dashboard_Total_PL";
    if(ObjectCreate(0, total_pl_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, total_pl_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, total_pl_name, OBJPROP_YDISTANCE, Dashboard_Y + 135);
        ObjectSetString(0, total_pl_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, total_pl_name, OBJPROP_FONTSIZE, Dashboard_Font_Size);
        ObjectSetInteger(0, total_pl_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    }

    //--- Scanner toggle button
    string scanner_btn_name = "Dashboard_Scanner_Button";
    if(ObjectCreate(0, scanner_btn_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_XDISTANCE, Dashboard_X + 10);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_YDISTANCE, Dashboard_Y + 155);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_XSIZE, 80);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_YSIZE, 20);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_BGCOLOR, scanner_panel_visible ? clrGreen : clrGray);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_BORDER_COLOR, clrDarkGray);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, scanner_btn_name, OBJPROP_BACK, false);
    }

    //--- Scanner button text
    string scanner_btn_text_name = "Dashboard_Scanner_Button_Text";
    if(ObjectCreate(0, scanner_btn_text_name, OBJ_LABEL, 0, 0, 0))
    {
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_XDISTANCE, Dashboard_X + 50);
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_YDISTANCE, Dashboard_Y + 165);
        ObjectSetString(0, scanner_btn_text_name, OBJPROP_TEXT, scanner_panel_visible ? "Scanner ON" : "Scanner OFF");
        ObjectSetString(0, scanner_btn_text_name, OBJPROP_FONT, "Arial Bold");
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_FONTSIZE, Dashboard_Font_Size - 1);
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_COLOR, clrWhite);
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_CORNER, CORNER_LEFT_UPPER);
        ObjectSetInteger(0, scanner_btn_text_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
    }
}

//+------------------------------------------------------------------+
//| Update Dashboard                                                |
//+------------------------------------------------------------------+
void UpdateDashboard()
{
    //--- Update trend display
    string trend_text = "Current Trend: ";
    color trend_color = clrGray;

    if(current_trend == 1)
    {
        trend_text += "🟢 BUY";
        trend_color = clrGreen;
    }
    else if(current_trend == -1)
    {
        trend_text += "🔴 SELL";
        trend_color = clrRed;
    }
    else
    {
        trend_text += "🟡 NEUTRAL";
        trend_color = clrOrange;
    }

    ObjectSetString(0, "Dashboard_Trend", OBJPROP_TEXT, trend_text);
    ObjectSetInteger(0, "Dashboard_Trend", OBJPROP_COLOR, trend_color);

    //--- Update trend duration
    string duration_text = "Trend Duration: " + FormatDuration(TimeCurrent() - trend_start_time);
    ObjectSetString(0, "Dashboard_Duration", OBJPROP_TEXT, duration_text);

    //--- Update last signal
    string signal_text = "Last Signal: ";
    if(last_signal_type == 1)
        signal_text += "BUY at " + TimeToString(last_signal_time, TIME_MINUTES);
    else if(last_signal_type == -1)
        signal_text += "SELL at " + TimeToString(last_signal_time, TIME_MINUTES);
    else
        signal_text += "None";

    ObjectSetString(0, "Dashboard_Signal", OBJPROP_TEXT, signal_text);

    //--- Update active trades
    int buy_count = CountPositions(POSITION_TYPE_BUY);
    int sell_count = CountPositions(POSITION_TYPE_SELL);
    string trades_text = StringFormat("Active Trades: %d Buy | %d Sell", buy_count, sell_count);
    ObjectSetString(0, "Dashboard_Trades", OBJPROP_TEXT, trades_text);

    //--- Update P&L
    double buy_profit = CalculateProfit(POSITION_TYPE_BUY);
    double sell_profit = CalculateProfit(POSITION_TYPE_SELL);
    double total_profit = buy_profit + sell_profit;

    string pl_text = StringFormat("P&L: Buy $%.2f | Sell $%.2f", buy_profit, sell_profit);
    ObjectSetString(0, "Dashboard_PL", OBJPROP_TEXT, pl_text);

    string total_pl_text = StringFormat("Total P&L: $%.2f", total_profit);
    color pl_color = (total_profit >= 0) ? clrGreen : clrRed;
    ObjectSetString(0, "Dashboard_Total_PL", OBJPROP_TEXT, total_pl_text);
    ObjectSetInteger(0, "Dashboard_Total_PL", OBJPROP_COLOR, pl_color);

    //--- Update scanner button
    UpdateScannerButton();
}

//+------------------------------------------------------------------+
//| Update Scanner Button                                           |
//+------------------------------------------------------------------+
void UpdateScannerButton()
{
    //--- Update button background color
    color btn_color = scanner_panel_visible ? clrGreen : clrGray;
    ObjectSetInteger(0, "Dashboard_Scanner_Button", OBJPROP_BGCOLOR, btn_color);

    //--- Update button text
    string btn_text = scanner_panel_visible ? "Scanner ON" : "Scanner OFF";
    ObjectSetString(0, "Dashboard_Scanner_Button_Text", OBJPROP_TEXT, btn_text);
}

//+------------------------------------------------------------------+
//| Delete Dashboard                                                |
//+------------------------------------------------------------------+
void DeleteDashboard()
{
    ObjectDelete(0, "Dashboard_Background");
    ObjectDelete(0, "Dashboard_Title");
    ObjectDelete(0, "Dashboard_Trend");
    ObjectDelete(0, "Dashboard_Duration");
    ObjectDelete(0, "Dashboard_Signal");
    ObjectDelete(0, "Dashboard_Trades");
    ObjectDelete(0, "Dashboard_PL");
    ObjectDelete(0, "Dashboard_Total_PL");
    ObjectDelete(0, "Dashboard_Scanner_Button");
    ObjectDelete(0, "Dashboard_Scanner_Button_Text");
}

//+------------------------------------------------------------------+
//| Format Duration                                                 |
//+------------------------------------------------------------------+
string FormatDuration(int seconds)
{
    int hours = seconds / 3600;
    int minutes = (seconds % 3600) / 60;

    if(hours > 0)
        return StringFormat("%dh %dm", hours, minutes);
    else
        return StringFormat("%dm", minutes);
}

//+------------------------------------------------------------------+
//| Create Multi-Timeframe Scanner                                  |
//+------------------------------------------------------------------+
void CreateScanner()
{
    if(timeframe_count == 0) return;

    //--- Create main panel
    CreateScannerRectangle(SCANNER_MAIN_PANEL, Scanner_X, Scanner_Y, Scanner_Width, Scanner_Height, Scanner_Background);

    //--- Create header panel
    CreateScannerRectangle(SCANNER_HEADER_PANEL, Scanner_X, Scanner_Y, Scanner_Width, 27, Scanner_Header_Color);

    //--- Create header elements
    CreateScannerLabel(SCANNER_HEADER_ICON, CharToString(91), Scanner_X - 12, Scanner_Y + 14, 18, clrAqua, "Wingdings");
    CreateScannerLabel(SCANNER_HEADER_TEXT, "TimeframeScanner", Scanner_X - 105, Scanner_Y + 12, 13, clrWhite);
    CreateScannerLabel(SCANNER_CLOSE_BUTTON, CharToString('r'), Scanner_X - Scanner_Width + 32, Scanner_Y + 14, 18, clrYellow, "Webdings");

    //--- Create symbol header
    CreateScannerRectangle(SCANNER_SYMBOL_RECTANGLE, Scanner_X - 2, Scanner_Y + 35, SCANNER_WIDTH_TIMEFRAME, SCANNER_HEIGHT_RECTANGLE, clrGray);
    CreateScannerLabel(SCANNER_SYMBOL_TEXT, Symbol(), Scanner_X - 47, Scanner_Y + 45, Scanner_Font_Size, clrWhite);

    //--- Create column headers
    string header_names[] = {"BUY", "SELL", "RSI", "STOCH", "CCI", "ADX", "AO"};
    for(int header_index = 0; header_index < ArraySize(header_names); header_index++)
    {
        int x_offset = (Scanner_X - SCANNER_WIDTH_TIMEFRAME) - (header_index < 2 ? header_index * SCANNER_WIDTH_SIGNAL : 2 * SCANNER_WIDTH_SIGNAL + (header_index - 2) * SCANNER_WIDTH_INDICATOR) + (1 + header_index);
        int width = (header_index < 2 ? SCANNER_WIDTH_SIGNAL : SCANNER_WIDTH_INDICATOR);

        CreateScannerRectangle(SCANNER_HEADER_RECTANGLE + IntegerToString(header_index), x_offset, Scanner_Y + 35, width, SCANNER_HEIGHT_RECTANGLE, clrGray);
        CreateScannerLabel(SCANNER_HEADER_TEXT + IntegerToString(header_index), header_names[header_index], x_offset - width/2, Scanner_Y + 45, Scanner_Font_Size, clrWhite);
    }

    //--- Create timeframe rows
    for(int timeframe_index = 0; timeframe_index < timeframe_count; timeframe_index++)
    {
        CreateScannerTimeframeRow(timeframe_index);
    }
}

//+------------------------------------------------------------------+
//| Create Scanner Rectangle                                         |
//+------------------------------------------------------------------+
bool CreateScannerRectangle(string object_name, int x_distance, int y_distance, int x_size, int y_size, color background_color, color border_color = clrBlack)
{
    ResetLastError();
    if(!ObjectCreate(0, object_name, OBJ_RECTANGLE_LABEL, 0, 0, 0))
    {
        Print(__FUNCTION__, ": failed to create Rectangle: ERR Code: ", GetLastError());
        return false;
    }

    ObjectSetInteger(0, object_name, OBJPROP_XDISTANCE, x_distance);
    ObjectSetInteger(0, object_name, OBJPROP_YDISTANCE, y_distance);
    ObjectSetInteger(0, object_name, OBJPROP_XSIZE, x_size);
    ObjectSetInteger(0, object_name, OBJPROP_YSIZE, y_size);
    ObjectSetInteger(0, object_name, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
    ObjectSetInteger(0, object_name, OBJPROP_BGCOLOR, background_color);
    ObjectSetInteger(0, object_name, OBJPROP_BORDER_COLOR, border_color);
    ObjectSetInteger(0, object_name, OBJPROP_BORDER_TYPE, BORDER_FLAT);
    ObjectSetInteger(0, object_name, OBJPROP_BACK, false);

    return true;
}

//+------------------------------------------------------------------+
//| Create Scanner Label                                             |
//+------------------------------------------------------------------+
bool CreateScannerLabel(string object_name, string text, int x_distance, int y_distance, int font_size = 12, color text_color = clrBlack, string font = "Arial Rounded MT Bold")
{
    ResetLastError();
    if(!ObjectCreate(0, object_name, OBJ_LABEL, 0, 0, 0))
    {
        Print(__FUNCTION__, ": failed to create Label: ERR Code: ", GetLastError());
        return false;
    }

    ObjectSetInteger(0, object_name, OBJPROP_XDISTANCE, x_distance);
    ObjectSetInteger(0, object_name, OBJPROP_YDISTANCE, y_distance);
    ObjectSetInteger(0, object_name, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
    ObjectSetString(0, object_name, OBJPROP_TEXT, text);
    ObjectSetString(0, object_name, OBJPROP_FONT, font);
    ObjectSetInteger(0, object_name, OBJPROP_FONTSIZE, font_size);
    ObjectSetInteger(0, object_name, OBJPROP_COLOR, text_color);
    ObjectSetInteger(0, object_name, OBJPROP_ANCHOR, ANCHOR_CENTER);

    return true;
}

//+------------------------------------------------------------------+
//| Create Scanner Timeframe Row                                    |
//+------------------------------------------------------------------+
void CreateScannerTimeframeRow(int timeframe_index)
{
    //--- Highlight current timeframe
    color timeframe_background = (timeframe_enums[timeframe_index] == Period()) ? clrLimeGreen : clrGray;
    color timeframe_text_color = (timeframe_enums[timeframe_index] == Period()) ? clrBlack : clrWhite;

    int y_pos = (Scanner_Y + 35 + SCANNER_HEIGHT_RECTANGLE) + timeframe_index * SCANNER_HEIGHT_RECTANGLE - (1 + timeframe_index);

    //--- Create timeframe rectangle and label
    CreateScannerRectangle(SCANNER_TIMEFRAME_RECTANGLE + IntegerToString(timeframe_index), Scanner_X - 2, y_pos, SCANNER_WIDTH_TIMEFRAME, SCANNER_HEIGHT_RECTANGLE, timeframe_background);
    CreateScannerLabel(SCANNER_TIMEFRAME_TEXT + IntegerToString(timeframe_index), TruncateTimeframeName(timeframe_index), Scanner_X - 47, y_pos + 10, Scanner_Font_Size, timeframe_text_color);

    //--- Create indicator and signal cells
    string header_names[] = {"BUY", "SELL", "RSI", "STOCH", "CCI", "ADX", "AO"};
    for(int header_index = 0; header_index < ArraySize(header_names); header_index++)
    {
        string cell_rectangle_name, cell_text_name;
        color cell_background = (header_index < 2) ? C'230,230,230' : clrBlack;

        switch(header_index)
        {
            case 0: cell_rectangle_name = SCANNER_BUY_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_BUY_TEXT + IntegerToString(timeframe_index); break;
            case 1: cell_rectangle_name = SCANNER_SELL_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_SELL_TEXT + IntegerToString(timeframe_index); break;
            case 2: cell_rectangle_name = SCANNER_RSI_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_RSI_TEXT + IntegerToString(timeframe_index); break;
            case 3: cell_rectangle_name = SCANNER_STOCH_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_STOCH_TEXT + IntegerToString(timeframe_index); break;
            case 4: cell_rectangle_name = SCANNER_CCI_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_CCI_TEXT + IntegerToString(timeframe_index); break;
            case 5: cell_rectangle_name = SCANNER_ADX_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_ADX_TEXT + IntegerToString(timeframe_index); break;
            case 6: cell_rectangle_name = SCANNER_AO_RECTANGLE + IntegerToString(timeframe_index); cell_text_name = SCANNER_AO_TEXT + IntegerToString(timeframe_index); break;
        }

        int x_offset = (Scanner_X - SCANNER_WIDTH_TIMEFRAME) - (header_index < 2 ? header_index * SCANNER_WIDTH_SIGNAL : 2 * SCANNER_WIDTH_SIGNAL + (header_index - 2) * SCANNER_WIDTH_INDICATOR) + (1 + header_index);
        int width = (header_index < 2 ? SCANNER_WIDTH_SIGNAL : SCANNER_WIDTH_INDICATOR);

        CreateScannerRectangle(cell_rectangle_name, x_offset, y_pos, width, SCANNER_HEIGHT_RECTANGLE, cell_background);
        CreateScannerLabel(cell_text_name, "-/-", x_offset - width/2, y_pos + 10, 10, clrWhite);
    }
}





//+------------------------------------------------------------------+
//| Update Multi-Timeframe Scanner                                  |
//+------------------------------------------------------------------+
void UpdateScanner()
{
    if(!scanner_panel_visible) return;

    for(int timeframe_index = 0; timeframe_index < timeframe_count; timeframe_index++)
    {
        ENUM_TIMEFRAMES tf = timeframe_enums[timeframe_index];

        //--- Initialize indicator handles
        int rsi_handle = iRSI(Symbol(), tf, 14, PRICE_CLOSE);
        int stoch_handle = iStochastic(Symbol(), tf, 14, 3, 3, MODE_SMA, STO_LOWHIGH);
        int cci_handle = iCCI(Symbol(), tf, 20, PRICE_TYPICAL);
        int adx_handle = iADX(Symbol(), tf, 14);
        int ao_handle = iAO(Symbol(), tf);

        //--- Check for valid handles
        if(rsi_handle == INVALID_HANDLE || stoch_handle == INVALID_HANDLE ||
           cci_handle == INVALID_HANDLE || adx_handle == INVALID_HANDLE ||
           ao_handle == INVALID_HANDLE)
        {
            Print("Failed to create indicator handle for timeframe ", TruncateTimeframeName(timeframe_index));
            continue;
        }

        //--- Copy indicator values
        if(CopyBuffer(rsi_handle, 0, 0, 1, scanner_rsi_values) <= 0 ||
           CopyBuffer(stoch_handle, 1, 0, 1, scanner_stoch_values) <= 0 ||
           CopyBuffer(cci_handle, 0, 0, 1, scanner_cci_values) <= 0 ||
           CopyBuffer(adx_handle, 0, 0, 1, scanner_adx_values) <= 0 ||
           CopyBuffer(ao_handle, 0, 0, 1, scanner_ao_values) <= 0)
        {
            Print("Failed to copy buffer for timeframe ", TruncateTimeframeName(timeframe_index));
            IndicatorRelease(rsi_handle);
            IndicatorRelease(stoch_handle);
            IndicatorRelease(cci_handle);
            IndicatorRelease(adx_handle);
            IndicatorRelease(ao_handle);
            continue;
        }

        //--- Update RSI
        color rsi_color = (scanner_rsi_values[0] < 30) ? clrBlue : (scanner_rsi_values[0] > 70) ? clrRed : clrWhite;
        UpdateScannerLabel(SCANNER_RSI_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_rsi_values[0], 2), rsi_color);

        //--- Update Stochastic
        color stoch_color = (scanner_stoch_values[0] < 20) ? clrBlue : (scanner_stoch_values[0] > 80) ? clrRed : clrWhite;
        UpdateScannerLabel(SCANNER_STOCH_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_stoch_values[0], 2), stoch_color);

        //--- Update CCI
        color cci_color = (scanner_cci_values[0] < -100) ? clrBlue : (scanner_cci_values[0] > 100) ? clrRed : clrWhite;
        UpdateScannerLabel(SCANNER_CCI_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_cci_values[0], 2), cci_color);

        //--- Update ADX
        color adx_color = (scanner_adx_values[0] > 25) ? clrBlue : clrWhite;
        UpdateScannerLabel(SCANNER_ADX_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_adx_values[0], 2), adx_color);

        //--- Update AO
        color ao_color = (scanner_ao_values[0] > 0) ? clrGreen : (scanner_ao_values[0] < 0) ? clrRed : clrWhite;
        UpdateScannerLabel(SCANNER_AO_TEXT + IntegerToString(timeframe_index), DoubleToString(scanner_ao_values[0], 2), ao_color);

        //--- Calculate and update Buy/Sell signals
        string buy_signal = CalculateScannerSignalStrength(scanner_rsi_values[0], scanner_stoch_values[0], scanner_cci_values[0], scanner_adx_values[0], scanner_ao_values[0], true);
        string sell_signal = CalculateScannerSignalStrength(scanner_rsi_values[0], scanner_stoch_values[0], scanner_cci_values[0], scanner_adx_values[0], scanner_ao_values[0], false);

        //--- Update Buy signal
        color buy_background = (buy_signal == "Strong Buy") ? clrGreen : (buy_signal == "Buy") ? clrSeaGreen : C'105,105,105';
        UpdateScannerRectangle(SCANNER_BUY_RECTANGLE + IntegerToString(timeframe_index), buy_background);
        UpdateScannerLabel(SCANNER_BUY_TEXT + IntegerToString(timeframe_index), buy_signal, clrWhite);

        //--- Update Sell signal
        color sell_background = (sell_signal == "Strong Sell") ? clrRed : (sell_signal == "Sell") ? clrSalmon : C'105,105,105';
        UpdateScannerRectangle(SCANNER_SELL_RECTANGLE + IntegerToString(timeframe_index), sell_background);
        UpdateScannerLabel(SCANNER_SELL_TEXT + IntegerToString(timeframe_index), sell_signal, clrWhite);

        //--- Release indicator handles
        IndicatorRelease(rsi_handle);
        IndicatorRelease(stoch_handle);
        IndicatorRelease(cci_handle);
        IndicatorRelease(adx_handle);
        IndicatorRelease(ao_handle);
    }
}

//+------------------------------------------------------------------+
//| Calculate Scanner Signal Strength                               |
//+------------------------------------------------------------------+
string CalculateScannerSignalStrength(double rsi, double stochastic, double cci, double adx, double ao, bool is_buy)
{
    int signal_strength = 0;

    if(is_buy && rsi < 40) signal_strength++;
    else if(!is_buy && rsi > 60) signal_strength++;

    if(is_buy && stochastic < 40) signal_strength++;
    else if(!is_buy && stochastic > 60) signal_strength++;

    if(is_buy && cci < -70) signal_strength++;
    else if(!is_buy && cci > 70) signal_strength++;

    if(adx > 40) signal_strength++;

    if(is_buy && ao > 0) signal_strength++;
    else if(!is_buy && ao < 0) signal_strength++;

    if(signal_strength >= 3) return is_buy ? "Strong Buy" : "Strong Sell";
    if(signal_strength >= 2) return is_buy ? "Buy" : "Sell";
    return "Neutral";
}

//+------------------------------------------------------------------+
//| Update Scanner Rectangle                                         |
//+------------------------------------------------------------------+
bool UpdateScannerRectangle(string object_name, color background_color)
{
    int found = ObjectFind(0, object_name);
    if(found < 0)
    {
        ResetLastError();
        Print("UNABLE TO FIND THE RECTANGLE: ", object_name, ". ERR Code: ", GetLastError());
        return false;
    }
    ObjectSetInteger(0, object_name, OBJPROP_BGCOLOR, background_color);
    return true;
}

//+------------------------------------------------------------------+
//| Update Scanner Label                                             |
//+------------------------------------------------------------------+
bool UpdateScannerLabel(string object_name, string text, color text_color)
{
    int found = ObjectFind(0, object_name);
    if(found < 0)
    {
        ResetLastError();
        Print("UNABLE TO FIND THE LABEL: ", object_name, ". ERR Code: ", GetLastError());
        return false;
    }
    ObjectSetString(0, object_name, OBJPROP_TEXT, text);
    ObjectSetInteger(0, object_name, OBJPROP_COLOR, text_color);
    return true;
}





//+------------------------------------------------------------------+
//| Delete Scanner                                                  |
//+------------------------------------------------------------------+
void DeleteScanner()
{
    ObjectDelete(0, SCANNER_MAIN_PANEL);
    ObjectDelete(0, SCANNER_HEADER_PANEL);
    ObjectDelete(0, SCANNER_HEADER_ICON);
    ObjectDelete(0, SCANNER_HEADER_TEXT);
    ObjectDelete(0, SCANNER_CLOSE_BUTTON);

    ObjectsDeleteAll(0, SCANNER_SYMBOL_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_SYMBOL_TEXT);
    ObjectsDeleteAll(0, SCANNER_TIMEFRAME_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_TIMEFRAME_TEXT);
    ObjectsDeleteAll(0, SCANNER_HEADER_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_HEADER_TEXT);
    ObjectsDeleteAll(0, SCANNER_RSI_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_RSI_TEXT);
    ObjectsDeleteAll(0, SCANNER_STOCH_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_STOCH_TEXT);
    ObjectsDeleteAll(0, SCANNER_CCI_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_CCI_TEXT);
    ObjectsDeleteAll(0, SCANNER_ADX_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_ADX_TEXT);
    ObjectsDeleteAll(0, SCANNER_AO_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_AO_TEXT);
    ObjectsDeleteAll(0, SCANNER_BUY_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_BUY_TEXT);
    ObjectsDeleteAll(0, SCANNER_SELL_RECTANGLE);
    ObjectsDeleteAll(0, SCANNER_SELL_TEXT);

    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Draw Trend Lines                                               |
//+------------------------------------------------------------------+
void DrawTrendLines(double up_value, double dn_value)
{
    if(!Show_Trend_Lines) return;

    datetime time_current = iTime(Symbol(), PERIOD_CURRENT, 0);
    datetime time_prev = iTime(Symbol(), PERIOD_CURRENT, 1);

    //--- Draw continuous Supertrend line
    if(current_trend == 1)
    {
        // Draw green line for bullish trend
        string up_line_name = "Supertrend_Line_" + IntegerToString(time_current);
        if(ObjectCreate(0, up_line_name, OBJ_TREND, 0, time_prev, up_value, time_current, up_value))
        {
            ObjectSetInteger(0, up_line_name, OBJPROP_COLOR, clrLime);
            ObjectSetInteger(0, up_line_name, OBJPROP_WIDTH, Trend_Line_Width);
            ObjectSetInteger(0, up_line_name, OBJPROP_RAY_RIGHT, false);
            ObjectSetInteger(0, up_line_name, OBJPROP_STYLE, STYLE_SOLID);
        }
    }
    else if(current_trend == -1)
    {
        // Draw red line for bearish trend
        string dn_line_name = "Supertrend_Line_" + IntegerToString(time_current);
        if(ObjectCreate(0, dn_line_name, OBJ_TREND, 0, time_prev, dn_value, time_current, dn_value))
        {
            ObjectSetInteger(0, dn_line_name, OBJPROP_COLOR, clrRed);
            ObjectSetInteger(0, dn_line_name, OBJPROP_WIDTH, Trend_Line_Width);
            ObjectSetInteger(0, dn_line_name, OBJPROP_RAY_RIGHT, false);
            ObjectSetInteger(0, dn_line_name, OBJPROP_STYLE, STYLE_SOLID);
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Signals                                                   |
//+------------------------------------------------------------------+
void DrawSignals()
{
    if(!Show_Buy_Sell_Signals) return;

    datetime time_current = iTime(Symbol(), PERIOD_CURRENT, 0);

    //--- Draw buy signal
    if(CheckBuySignal())
    {
        DrawBuySignal(time_current);
    }

    //--- Draw sell signal
    if(CheckSellSignal())
    {
        DrawSellSignal(time_current);
    }
}

//+------------------------------------------------------------------+
//| Draw Buy Signal with Label                                      |
//+------------------------------------------------------------------+
void DrawBuySignal(datetime signal_time)
{
    double low_price = iLow(Symbol(), PERIOD_CURRENT, 0);
    double signal_price = low_price - (20 * Point()); // Position below candle

    //--- Create buy arrow
    string buy_arrow_name = "Buy_Signal_Arrow_" + IntegerToString(signal_time);
    if(ObjectCreate(0, buy_arrow_name, OBJ_ARROW_UP, 0, signal_time, signal_price))
    {
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_COLOR, Buy_Signal_Color);
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_ARROWCODE, 233); // Up arrow
    }

    //--- Create buy label (if enabled)
    if(Show_Signal_Labels)
    {
        string buy_label_name = "Buy_Signal_Label_" + IntegerToString(signal_time);
        if(ObjectCreate(0, buy_label_name, OBJ_TEXT, 0, signal_time, signal_price - (30 * Point())))
        {
            ObjectSetString(0, buy_label_name, OBJPROP_TEXT, "BUY");
            ObjectSetString(0, buy_label_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, buy_label_name, OBJPROP_FONTSIZE, Signal_Label_Size);
            ObjectSetInteger(0, buy_label_name, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, buy_label_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
        }

        //--- Create background rectangle for label (if enabled)
        if(Show_Signal_Background)
        {
            string buy_bg_name = "Buy_Signal_BG_" + IntegerToString(signal_time);
            if(ObjectCreate(0, buy_bg_name, OBJ_RECTANGLE, 0, signal_time - 300, signal_price - (40 * Point()),
                            signal_time + 300, signal_price - (20 * Point())))
            {
                ObjectSetInteger(0, buy_bg_name, OBJPROP_COLOR, Buy_Signal_Color);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_BGCOLOR, Buy_Signal_Color);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_BACK, true);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_FILL, true);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Sell Signal with Label                                     |
//+------------------------------------------------------------------+
void DrawSellSignal(datetime signal_time)
{
    double high_price = iHigh(Symbol(), PERIOD_CURRENT, 0);
    double signal_price = high_price + (20 * Point()); // Position above candle

    //--- Create sell arrow
    string sell_arrow_name = "Sell_Signal_Arrow_" + IntegerToString(signal_time);
    if(ObjectCreate(0, sell_arrow_name, OBJ_ARROW_DOWN, 0, signal_time, signal_price))
    {
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_COLOR, Sell_Signal_Color);
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_ARROWCODE, 234); // Down arrow
    }

    //--- Create sell label (if enabled)
    if(Show_Signal_Labels)
    {
        string sell_label_name = "Sell_Signal_Label_" + IntegerToString(signal_time);
        if(ObjectCreate(0, sell_label_name, OBJ_TEXT, 0, signal_time, signal_price + (30 * Point())))
        {
            ObjectSetString(0, sell_label_name, OBJPROP_TEXT, "SELL");
            ObjectSetString(0, sell_label_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, sell_label_name, OBJPROP_FONTSIZE, Signal_Label_Size);
            ObjectSetInteger(0, sell_label_name, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, sell_label_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
        }

        //--- Create background rectangle for label (if enabled)
        if(Show_Signal_Background)
        {
            string sell_bg_name = "Sell_Signal_BG_" + IntegerToString(signal_time);
            if(ObjectCreate(0, sell_bg_name, OBJ_RECTANGLE, 0, signal_time - 300, signal_price + (20 * Point()),
                            signal_time + 300, signal_price + (40 * Point())))
            {
                ObjectSetInteger(0, sell_bg_name, OBJPROP_COLOR, Sell_Signal_Color);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_BGCOLOR, Sell_Signal_Color);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_BACK, true);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_FILL, true);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Chart Event Handler                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long& lparam, const double& dparam, const string& sparam)
{
    if(id == CHARTEVENT_OBJECT_CLICK)
    {
        if(sparam == SCANNER_CLOSE_BUTTON)
        {
            Print("Closing the scanner panel now");
            PlaySound("alert.wav");
            scanner_panel_visible = false;
            DeleteScanner();
            UpdateScannerButton();
            ChartRedraw();
        }
        else if(sparam == "Dashboard_Scanner_Button" || sparam == "Dashboard_Scanner_Button_Text")
        {
            ToggleScanner();
        }
    }
}

//+------------------------------------------------------------------+
//| Toggle Scanner Visibility                                       |
//+------------------------------------------------------------------+
void ToggleScanner()
{
    if(scanner_panel_visible)
    {
        Print("Hiding scanner panel");
        scanner_panel_visible = false;
        DeleteScanner();
    }
    else
    {
        Print("Showing scanner panel");
        scanner_panel_visible = true;
        if(Enable_Scanner)
        {
            CreateScanner();
        }
    }

    UpdateScannerButton();
    PlaySound("alert.wav");
    ChartRedraw();
}

//+------------------------------------------------------------------+
//| Clean up Signal Objects                                         |
//+------------------------------------------------------------------+
void CleanupSignalObjects()
{
    //--- Remove old signal objects to prevent chart clutter
    ObjectsDeleteAll(0, "Buy_Signal_Arrow_");
    ObjectsDeleteAll(0, "Buy_Signal_Label_");
    ObjectsDeleteAll(0, "Buy_Signal_BG_");
    ObjectsDeleteAll(0, "Sell_Signal_Arrow_");
    ObjectsDeleteAll(0, "Sell_Signal_Label_");
    ObjectsDeleteAll(0, "Sell_Signal_BG_");
    ObjectsDeleteAll(0, "Hist_Buy_Signal_Arrow_");
    ObjectsDeleteAll(0, "Hist_Buy_Signal_Label_");
    ObjectsDeleteAll(0, "Hist_Buy_Signal_BG_");
    ObjectsDeleteAll(0, "Hist_Sell_Signal_Arrow_");
    ObjectsDeleteAll(0, "Hist_Sell_Signal_Label_");
    ObjectsDeleteAll(0, "Hist_Sell_Signal_BG_");
    ObjectsDeleteAll(0, "Supertrend_Line_");
}

//+------------------------------------------------------------------+
//| Calculate Historical Signals                                    |
//+------------------------------------------------------------------+
void CalculateHistoricalSignals()
{
    int bars_to_analyze = MathMax(50, MathMin(Historical_Bars, 1000)); // Limit between 50-1000
    int available_bars = iBars(Symbol(), PERIOD_CURRENT);
    if(available_bars < bars_to_analyze)
        bars_to_analyze = available_bars - 1;

    Print("Calculating historical Supertrend signals for ", bars_to_analyze, " bars...");

    //--- Arrays for historical calculation
    double hist_high[], hist_low[], hist_close[];
    double hist_atr[];
    double hist_up_trend[], hist_down_trend[];
    int hist_trend[];

    //--- Set arrays as series
    ArraySetAsSeries(hist_high, true);
    ArraySetAsSeries(hist_low, true);
    ArraySetAsSeries(hist_close, true);
    ArraySetAsSeries(hist_atr, true);
    ArraySetAsSeries(hist_up_trend, true);
    ArraySetAsSeries(hist_down_trend, true);
    ArraySetAsSeries(hist_trend, true);

    //--- Resize arrays
    ArrayResize(hist_high, bars_to_analyze);
    ArrayResize(hist_low, bars_to_analyze);
    ArrayResize(hist_close, bars_to_analyze);
    ArrayResize(hist_atr, bars_to_analyze);
    ArrayResize(hist_up_trend, bars_to_analyze);
    ArrayResize(hist_down_trend, bars_to_analyze);
    ArrayResize(hist_trend, bars_to_analyze);

    //--- Get historical price data
    if(CopyHigh(Symbol(), PERIOD_CURRENT, 0, bars_to_analyze, hist_high) < bars_to_analyze ||
       CopyLow(Symbol(), PERIOD_CURRENT, 0, bars_to_analyze, hist_low) < bars_to_analyze ||
       CopyClose(Symbol(), PERIOD_CURRENT, 0, bars_to_analyze, hist_close) < bars_to_analyze)
    {
        Print("Failed to copy historical price data");
        return;
    }

    //--- Get historical ATR data
    if(CopyBuffer(atr_handle, 0, 0, bars_to_analyze, hist_atr) < bars_to_analyze)
    {
        Print("Failed to copy historical ATR data");
        return;
    }

    //--- Calculate historical Supertrend values
    double prev_up_hist = 0;
    double prev_down_hist = 0;
    int prev_trend_hist = 1; // Start with bullish trend

    for(int i = bars_to_analyze - 1; i >= 0; i--)
    {
        //--- Calculate source price
        double src = 0;
        switch(Source_Price)
        {
            case PRICE_CLOSE: src = hist_close[i]; break;
            case PRICE_HIGH: src = hist_high[i]; break;
            case PRICE_LOW: src = hist_low[i]; break;
            case PRICE_MEDIAN: src = (hist_high[i] + hist_low[i]) / 2.0; break;
            case PRICE_TYPICAL: src = (hist_high[i] + hist_low[i] + hist_close[i]) / 3.0; break;
            case PRICE_WEIGHTED: src = (hist_high[i] + hist_low[i] + 2 * hist_close[i]) / 4.0; break;
            default: src = (hist_high[i] + hist_low[i]) / 2.0; break;
        }

        //--- Calculate basic upper and lower bands
        double up = src - (ATR_Multiplier * hist_atr[i]);
        double dn = src + (ATR_Multiplier * hist_atr[i]);

        //--- Apply Supertrend logic
        if(prev_up_hist == 0) prev_up_hist = up;
        if(prev_down_hist == 0) prev_down_hist = dn;

        if(i < bars_to_analyze - 1) // Not the first bar
        {
            up = (hist_close[i+1] > prev_up_hist) ? MathMax(up, prev_up_hist) : up;
            dn = (hist_close[i+1] < prev_down_hist) ? MathMin(dn, prev_down_hist) : dn;
        }

        //--- Store values
        hist_up_trend[i] = up;
        hist_down_trend[i] = dn;

        //--- Determine trend
        int current_trend_hist = prev_trend_hist;

        if(current_trend_hist == -1 && hist_close[i] > prev_down_hist)
            current_trend_hist = 1;
        else if(current_trend_hist == 1 && hist_close[i] < prev_up_hist)
            current_trend_hist = -1;

        hist_trend[i] = current_trend_hist;

        //--- Check for trend change and draw signal
        if(i < bars_to_analyze - 1 && current_trend_hist != prev_trend_hist)
        {
            datetime signal_time = iTime(Symbol(), PERIOD_CURRENT, i);

            if(current_trend_hist == 1) // Buy signal
            {
                DrawHistoricalBuySignal(signal_time, i);
            }
            else if(current_trend_hist == -1) // Sell signal
            {
                DrawHistoricalSellSignal(signal_time, i);
            }
        }

        //--- Update previous values
        prev_up_hist = up;
        prev_down_hist = dn;
        prev_trend_hist = current_trend_hist;
    }

    //--- Display calculation summary
    int signal_count = 0;
    for(int obj = 0; obj < ObjectsTotal(0); obj++)
    {
        string obj_name = ObjectName(0, obj);
        if(StringFind(obj_name, "Hist_Buy_Signal_Arrow_") >= 0 || StringFind(obj_name, "Hist_Sell_Signal_Arrow_") >= 0)
            signal_count++;
    }

    Print("Historical signal calculation completed.");
    Print("Analyzed ", bars_to_analyze, " bars and found ", signal_count, " signals.");
    Print("Supertrend settings: ATR Period=", ATR_Period, ", Multiplier=", ATR_Multiplier, ", Source=", EnumToString(Source_Price));
}

//+------------------------------------------------------------------+
//| Draw Historical Buy Signal                                      |
//+------------------------------------------------------------------+
void DrawHistoricalBuySignal(datetime signal_time, int bar_index)
{
    double low_price = iLow(Symbol(), PERIOD_CURRENT, bar_index);
    double signal_price = low_price - (20 * Point());

    //--- Create buy arrow
    string buy_arrow_name = "Hist_Buy_Signal_Arrow_" + IntegerToString(signal_time);
    if(ObjectCreate(0, buy_arrow_name, OBJ_ARROW_UP, 0, signal_time, signal_price))
    {
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_COLOR, Buy_Signal_Color);
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        ObjectSetInteger(0, buy_arrow_name, OBJPROP_ARROWCODE, 233);
    }

    //--- Create buy label (if enabled)
    if(Show_Signal_Labels)
    {
        string buy_label_name = "Hist_Buy_Signal_Label_" + IntegerToString(signal_time);
        if(ObjectCreate(0, buy_label_name, OBJ_TEXT, 0, signal_time, signal_price - (30 * Point())))
        {
            ObjectSetString(0, buy_label_name, OBJPROP_TEXT, "BUY");
            ObjectSetString(0, buy_label_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, buy_label_name, OBJPROP_FONTSIZE, Signal_Label_Size);
            ObjectSetInteger(0, buy_label_name, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, buy_label_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
        }

        //--- Create background rectangle (if enabled)
        if(Show_Signal_Background)
        {
            string buy_bg_name = "Hist_Buy_Signal_BG_" + IntegerToString(signal_time);
            if(ObjectCreate(0, buy_bg_name, OBJ_RECTANGLE, 0, signal_time - 300, signal_price - (40 * Point()),
                            signal_time + 300, signal_price - (20 * Point())))
            {
                ObjectSetInteger(0, buy_bg_name, OBJPROP_COLOR, Buy_Signal_Color);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_BGCOLOR, Buy_Signal_Color);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_BACK, true);
                ObjectSetInteger(0, buy_bg_name, OBJPROP_FILL, true);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Draw Historical Sell Signal                                     |
//+------------------------------------------------------------------+
void DrawHistoricalSellSignal(datetime signal_time, int bar_index)
{
    double high_price = iHigh(Symbol(), PERIOD_CURRENT, bar_index);
    double signal_price = high_price + (20 * Point());

    //--- Create sell arrow
    string sell_arrow_name = "Hist_Sell_Signal_Arrow_" + IntegerToString(signal_time);
    if(ObjectCreate(0, sell_arrow_name, OBJ_ARROW_DOWN, 0, signal_time, signal_price))
    {
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_COLOR, Sell_Signal_Color);
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_WIDTH, Signal_Arrow_Size);
        ObjectSetInteger(0, sell_arrow_name, OBJPROP_ARROWCODE, 234);
    }

    //--- Create sell label (if enabled)
    if(Show_Signal_Labels)
    {
        string sell_label_name = "Hist_Sell_Signal_Label_" + IntegerToString(signal_time);
        if(ObjectCreate(0, sell_label_name, OBJ_TEXT, 0, signal_time, signal_price + (30 * Point())))
        {
            ObjectSetString(0, sell_label_name, OBJPROP_TEXT, "SELL");
            ObjectSetString(0, sell_label_name, OBJPROP_FONT, "Arial Bold");
            ObjectSetInteger(0, sell_label_name, OBJPROP_FONTSIZE, Signal_Label_Size);
            ObjectSetInteger(0, sell_label_name, OBJPROP_COLOR, clrWhite);
            ObjectSetInteger(0, sell_label_name, OBJPROP_ANCHOR, ANCHOR_CENTER);
        }

        //--- Create background rectangle (if enabled)
        if(Show_Signal_Background)
        {
            string sell_bg_name = "Hist_Sell_Signal_BG_" + IntegerToString(signal_time);
            if(ObjectCreate(0, sell_bg_name, OBJ_RECTANGLE, 0, signal_time - 300, signal_price + (20 * Point()),
                            signal_time + 300, signal_price + (40 * Point())))
            {
                ObjectSetInteger(0, sell_bg_name, OBJPROP_COLOR, Sell_Signal_Color);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_BGCOLOR, Sell_Signal_Color);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_BACK, true);
                ObjectSetInteger(0, sell_bg_name, OBJPROP_FILL, true);
            }
        }
    }
}
