# Supertrend Expert Advisor for MetaTrader 5

A comprehensive trading system based on the popular Supertrend indicator, featuring automated trading, real-time dashboard, and multi-timeframe analysis.

## 🚀 Features

### Core Trading System
- **Automated Trading**: Opens buy/sell orders based on Supertrend trend reversals
- **Hedging Support**: Allows multiple positions in the same direction
- **Advanced Trade Management**: Trailing stops, take profit, and stop loss
- **Customizable Parameters**: Full control over trading logic and risk management

### Real-Time Dashboard
- **Current Trend Display**: Visual indication of market direction (starts with BUY as default)
- **Trend Duration**: Time tracking since last trend change
- **Trade Statistics**: Active positions count and profit/loss breakdown
- **Performance Metrics**: Real-time P&L monitoring with color coding
- **Scanner Toggle**: Interactive button to show/hide the scanner panel (OFF by default)
- **Manual Trading Buttons**: BUY, SELL, and CLOSE ALL buttons for manual trade control

### Multi-Timeframe Scanner
- **Current Symbol Analysis**: Monitor current chart symbol across multiple timeframes
- **Technical Indicators**: RSI, Stochastic, CCI, ADX, and Awesome Oscillator
- **Buy/Sell Signals**: Automated signal strength calculation per timeframe
- **Real-Time Updates**: Automatic refresh every 5 seconds (configurable)
- **Professional Layout**: Grid-based interface based on MQL5 article 18319

### Chart Visualization
- **Enhanced Signal Display**: Professional BUY/SELL arrows with labels and backgrounds
- **Historical Signal Analysis**: Shows past signals based on current Supertrend settings
- **TradingView-Style Signals**: Clear, prominent signals similar to popular platforms
- **Supertrend Validation**: Visual confirmation that EA calculates Supertrend correctly
- **Signal Customization**: Control arrow size, label size, and background visibility
- **Configurable History**: Analyze 50-1000 historical bars for signal display
- **Zone Highlighting**: Background color changes based on trend direction
- **Customizable Appearance**: Full control over colors, sizes, and styles

## 📁 Project Structure

```
Supertrend/
├── Experts/
│   └── SupertrendEA.mq5           # Complete Expert Advisor (Single File)
├── Presets/
│   └── SupertrendEA.set           # Default settings file
├── Documentation/
│   ├── Installation.md            # Setup instructions
│   ├── UserGuide.md              # Comprehensive user manual
│   └── QuickStart.md             # 5-minute setup guide
├── Input/
│   ├── Requirments.rtf           # Original requirements
│   └── st.txt                    # Pine Script source
└── README.md                     # This file
```

## 🛠️ Installation

### Quick Setup
1. **Copy Files**:
   - `SupertrendEA.mq5` → `MQL5/Experts/`
   - `SupertrendEA.set` → `MQL5/Presets/`

2. **Compile**:
   - Open MetaEditor (F4)
   - Compile `SupertrendEA.mq5`
   - Verify no errors

3. **Attach to Chart**:
   - Drag EA onto desired chart
   - Load preset settings
   - Enable live trading

### Detailed Instructions
See [Installation.md](Documentation/Installation.md) for complete setup guide.

## ⚙️ Configuration

### Quick Start Settings

**Conservative (Low Risk)**:
```
Lot_Size = 0.01
ATR_Period = 14
ATR_Multiplier = 3.5
Take_Profit = 300
Stop_Loss = 200
```

**Aggressive (Higher Risk)**:
```
Lot_Size = 0.1
ATR_Period = 10
ATR_Multiplier = 2.5
Take_Profit = 800
Stop_Loss = 400
```

**Scalping (M1/M5)**:
```
Lot_Size = 0.05
ATR_Period = 8
ATR_Multiplier = 2.0
Take_Profit = 150
Stop_Loss = 100
```

## 📊 Dashboard Overview

The dashboard displays:
- **Current Trend**: 🟢 BUY / 🔴 SELL / 🟡 NEUTRAL (defaults to BUY)
- **Trend Duration**: Time since last trend change
- **Active Trades**: Count of open buy/sell positions
- **P&L Breakdown**: Separate and total profit/loss
- **Scanner Toggle**: Gray "Scanner OFF" by default, Green "Scanner ON" when active
- **Manual Trading**: 🟢 BUY, 🔴 SELL, 🟠 CLOSE ALL buttons for instant trade control

## 🔍 Scanner Features

Multi-timeframe analysis showing:
- **Current Symbol**: Analyzes the chart symbol across multiple timeframes
- **Timeframes**: M1, M5, M15, M20, M30, H1, H2, H3, H4, H8, H12, D1, W1
- **Buy/Sell Signals**: Strong Buy, Buy, Neutral, Sell, Strong Sell per timeframe
- **Technical Indicators**: RSI, Stochastic, CCI, ADX, AO values
- **Color Coding**: Blue for oversold/overbought, green/red for AO, white for neutral
- **Professional Grid**: Exact implementation from MQL5 article 18319

## 📈 Trading Strategy

### Entry Signals
- **Buy**: Trend changes from bearish to bullish
- **Sell**: Trend changes from bullish to bearish

### Exit Strategy
- **Take Profit**: Fixed level or trailing stop
- **Stop Loss**: Fixed level with optional trailing
- **Signal Reversal**: Close on opposite signal

### Risk Management
- Fixed or percentage-based position sizing
- Maximum daily loss limits
- Correlation-aware position management

## 🎯 Key Parameters

| Category | Parameter | Default | Description |
|----------|-----------|---------|-------------|
| **Supertrend** | ATR_Period | 10 | ATR calculation period |
| | ATR_Multiplier | 3.0 | Band distance multiplier |
| **Trading** | Lot_Size | 0.1 | Position size |
| | Take_Profit | 500 | TP in points |
| | Stop_Loss | 300 | SL in points |
| **Dashboard** | Show_Dashboard | true | Enable dashboard |
| | Dashboard_X | 20 | Horizontal position |
| **Scanner** | Enable_Scanner | true | Enable scanner |
| | Scanner_Timeframes | "M1,M5,M15,M20,M30,H1,H2,H3,H4,H8,H12,D1,W1" | Timeframes to scan |

## 📚 Documentation

- **[Installation Guide](Documentation/Installation.md)**: Complete setup instructions
- **[User Guide](Documentation/UserGuide.md)**: Detailed parameter reference and strategies
- **[Settings File](Presets/SupertrendEA.set)**: Default configuration

## ⚠️ Risk Warning

**Important**: Trading involves substantial risk of loss. This EA is provided for educational purposes. Always:
- Test thoroughly on demo account
- Use proper risk management
- Never risk more than you can afford to lose
- Monitor trades regularly
- Keep MT5 platform updated

## 🔧 System Requirements

- **Platform**: MetaTrader 5 (Build 3815+)
- **Account Type**: Hedging recommended
- **OS**: Windows 10/11
- **RAM**: Minimum 4GB
- **Connection**: Stable internet

## 📝 Version History

### v1.0.0 (Current)
- Initial release
- Complete Supertrend implementation
- Dashboard and scanner functionality
- Multi-timeframe analysis
- Advanced trade management
- Comprehensive documentation

## 🤝 Support

For technical support:
1. Check MT5 Journal for error messages
2. Verify all parameters are within valid ranges
3. Test on demo account first
4. Ensure broker compatibility

## 📄 License

This project is provided as-is for educational and research purposes. Use at your own risk.

## 🙏 Acknowledgments

- Based on the original Supertrend indicator concept
- Converted from Pine Script to MQL5
- Inspired by TradingView community indicators

---

**Happy Trading! 📈**

*Remember: The best strategy is the one you understand and can execute consistently with proper risk management.*
