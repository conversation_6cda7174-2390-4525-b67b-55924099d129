# Supertrend EA User Guide

## Table of Contents
1. [Overview](#overview)
2. [Parameter Reference](#parameter-reference)
3. [Dashboard Guide](#dashboard-guide)
4. [Scanner Guide](#scanner-guide)
5. [Trading Strategy](#trading-strategy)
6. [Risk Management](#risk-management)
7. [Optimization Tips](#optimization-tips)

## Overview

The Supertrend EA is a comprehensive trading system that automatically trades based on the Supertrend indicator. It provides:

- **Automated Trading**: Opens buy/sell orders on trend reversals
- **Real-time Dashboard**: Shows current trend, trade statistics, and P&L
- **Multi-timeframe Scanner**: Displays trend status across multiple timeframes
- **Advanced Trade Management**: Includes trailing stops and position management
- **Visual Indicators**: Chart signals and trend highlighting

## Parameter Reference

### 📊 Supertrend Settings

| Parameter | Default | Description |
|-----------|---------|-------------|
| ATR_Period | 10 | Number of periods for ATR calculation |
| ATR_Multiplier | 3.0 | Multiplier for ATR to create bands |
| Use_Smoothed_ATR | true | Use standard ATR (true) or SMA-based (false) |
| Source_Price | PRICE_MEDIAN | Price used for calculation (HL2 recommended) |

**Optimization Tips:**
- Lower ATR_Period (8-12): More sensitive, more signals
- Higher ATR_Period (14-20): Less sensitive, fewer false signals
- Lower ATR_Multiplier (2.0-2.5): Tighter bands, more signals
- Higher ATR_Multiplier (3.5-4.0): Wider bands, stronger signals

### 💰 Trading Settings

| Parameter | Default | Description |
|-----------|---------|-------------|
| Lot_Size | 0.1 | Fixed lot size for all trades |
| Take_Profit | 500 | Take profit in points |
| Stop_Loss | 300 | Stop loss in points |
| Enable_Trailing_Stop | false | Enable dynamic stop loss |
| Trailing_Stop_Distance | 200 | Distance for trailing stop (points) |
| Trailing_Stop_Step | 50 | Minimum step for trailing adjustment |
| Magic_Number | 12345 | Unique identifier for EA trades |
| Trade_Comment | "Supertrend EA" | Comment for all trades |
| Enable_Auto_Trading | true | Enable/disable automatic trading |
| Allow_Hedging | true | Allow multiple positions in same direction |

**Risk Management:**
- Start with small Lot_Size (0.01-0.05) for testing
- Set Stop_Loss based on account risk tolerance
- Use trailing stops for trend-following strategies
- Different Magic_Numbers for multiple EAs

### 📋 Dashboard Settings

| Parameter | Default | Description |
|-----------|---------|-------------|
| Show_Dashboard | true | Display dashboard panel |
| Dashboard_X | 20 | Horizontal position (pixels) |
| Dashboard_Y | 50 | Vertical position (pixels) |
| Dashboard_Width | 280 | Panel width (pixels) |
| Dashboard_Height | 180 | Panel height (pixels) |
| Dashboard_Background | Light Gray | Background color |
| Dashboard_Border | Dark Gray | Border color |
| Dashboard_Font_Size | 9 | Text font size |

### 🔍 Scanner Settings

| Parameter | Default | Description |
|-----------|---------|-------------|
| Enable_Scanner | true | Display multi-timeframe scanner |
| Scanner_Timeframes | "M1,M5,M15,H1,H4,D1,W1" | Timeframes to analyze |
| Scanner_X | 320 | Horizontal position (pixels) |
| Scanner_Y | 50 | Vertical position (pixels) |
| Scanner_Width | 420 | Panel width (pixels) |
| Scanner_Height | 220 | Panel height (pixels) |
| Scanner_Update_Seconds | 5 | Update frequency (seconds) |

**Timeframe Configuration:**
- **Scalping**: M1,M5,M15,M30
- **Day Trading**: M15,M30,H1,H4
- **Swing Trading**: H1,H4,D1,W1
- **Position Trading**: H4,D1,W1,MN1

### 🎨 Chart Visualization

| Parameter | Default | Description |
|-----------|---------|-------------|
| Show_Buy_Sell_Signals | true | Display signal arrows |
| Show_Trend_Lines | true | Display Supertrend lines |
| Highlight_Trend_Zones | true | Background color highlighting |
| Signal_Arrow_Size | 2 | Size of signal arrows (1-5) |
| Trend_Line_Width | 2 | Width of trend lines |
| Buy_Signal_Color | Lime | Color for buy signals |
| Sell_Signal_Color | Red | Color for sell signals |

## Dashboard Guide

The dashboard displays real-time information about the EA's performance:

### Dashboard Elements

1. **Current Trend**: 
   - 🟢 BUY: Uptrend active
   - 🔴 SELL: Downtrend active  
   - 🟡 NEUTRAL: Transition/sideways

2. **Trend Duration**: Time since last trend change

3. **Last Signal**: Direction and time of most recent signal

4. **Active Trades**: Count of open buy and sell positions

5. **P&L Display**: 
   - Separate profit/loss for buy and sell positions
   - Total combined P&L with color coding

### Dashboard Interpretation

- **Green Total P&L**: Profitable overall
- **Red Total P&L**: Losing overall
- **High Active Trades**: May indicate ranging market
- **Long Trend Duration**: Strong trending market

## Scanner Guide

The multi-timeframe scanner shows Supertrend status across different timeframes:

### Scanner Layout

| Column | Description |
|--------|-------------|
| Timeframe | Time period (M1, M5, H1, etc.) |
| Status | Current trend direction |
| Signal | Visual arrow indicator |
| Last Change | When trend last changed |

### Scanner Signals

- **🟢 BUY**: Bullish trend on this timeframe
- **🔴 SELL**: Bearish trend on this timeframe
- **🟡 NEUTRAL**: Transitioning or sideways

### Multi-Timeframe Analysis

**Trend Alignment Examples:**

1. **Strong Bullish Setup**:
   - All timeframes show 🟢 BUY
   - Higher probability of continued uptrend

2. **Mixed Signals**:
   - Lower timeframes: 🟢 BUY
   - Higher timeframes: 🔴 SELL
   - Possible pullback in larger downtrend

3. **Trend Reversal**:
   - Recent changes from 🔴 to 🟢 across timeframes
   - Potential new trend beginning

## Trading Strategy

### Entry Rules

**Buy Signal**:
- Supertrend changes from bearish to bullish
- Price closes above Supertrend line
- Optional: Confirm with higher timeframe alignment

**Sell Signal**:
- Supertrend changes from bullish to bearish
- Price closes below Supertrend line
- Optional: Confirm with higher timeframe alignment

### Exit Rules

**Take Profit**:
- Fixed TP level (recommended: 2-3x stop loss)
- Trailing stop to lock in profits
- Manual exit on opposite signal

**Stop Loss**:
- Fixed SL level below/above entry
- Trailing stop following trend
- Emergency exit on strong reversal

### Advanced Strategies

1. **Trend Following**:
   - Only trade in direction of higher timeframes
   - Use trailing stops to maximize profits
   - Avoid trading during news events

2. **Counter-Trend**:
   - Trade against short-term moves
   - Smaller position sizes
   - Quick profit-taking

3. **Breakout Trading**:
   - Wait for strong trend changes
   - Increase position size on confirmed breakouts
   - Use wider stops for volatility

## Risk Management

### Position Sizing

**Fixed Lot Method**:
```
Risk per trade = Account Balance × Risk %
Lot Size = Risk per trade ÷ (Stop Loss × Point Value)
```

**Percentage Risk Method**:
- Risk 1-2% of account per trade
- Adjust lot size based on stop loss distance
- Never risk more than 5% on single trade

### Money Management Rules

1. **Maximum Risk**: Never risk more than 2% per trade
2. **Daily Loss Limit**: Stop trading after 5% daily loss
3. **Position Limits**: Maximum 3-5 open positions
4. **Correlation**: Avoid multiple positions on correlated pairs

### Account Protection

- Use stop losses on all trades
- Enable trailing stops for trend trades
- Monitor news events and market volatility
- Regular account balance reviews

## Optimization Tips

### Backtesting

1. **Test Period**: Minimum 6 months of data
2. **Multiple Timeframes**: Test on M15, H1, H4
3. **Different Market Conditions**: Include trending and ranging periods
4. **Walk-Forward Analysis**: Optimize parameters progressively

### Parameter Optimization

**ATR Period Optimization**:
- Test range: 8-20
- Shorter periods: More signals, more noise
- Longer periods: Fewer signals, better quality

**ATR Multiplier Optimization**:
- Test range: 2.0-4.0
- Lower values: Earlier entries, more false signals
- Higher values: Later entries, stronger signals

### Performance Metrics

Monitor these key metrics:
- **Profit Factor**: Should be > 1.5
- **Win Rate**: Target 40-60%
- **Average Win/Loss Ratio**: Target > 1.5
- **Maximum Drawdown**: Keep < 20%
- **Sharpe Ratio**: Higher is better

### Common Optimizations

1. **Time Filters**: Avoid trading during low liquidity hours
2. **Volatility Filters**: Pause trading during extreme volatility
3. **Trend Filters**: Only trade with higher timeframe trend
4. **News Filters**: Avoid trading during high-impact news

## Troubleshooting

### Performance Issues

**Low Profit Factor**:
- Increase ATR_Multiplier for stronger signals
- Add trend filter from higher timeframe
- Optimize take profit/stop loss ratio

**High Drawdown**:
- Reduce position size
- Implement stricter risk management
- Add volatility filters

**Too Few Trades**:
- Decrease ATR_Multiplier
- Reduce ATR_Period
- Consider multiple timeframes

**Too Many False Signals**:
- Increase ATR_Multiplier
- Add confirmation filters
- Avoid ranging market conditions

Remember: Always test changes on demo account before applying to live trading!
