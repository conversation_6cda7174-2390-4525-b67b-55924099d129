# Supertrend EA Installation Guide

## Overview
The Supertrend Expert Advisor is a complete trading system based on the popular Supertrend indicator, featuring:
- Automated trading based on trend reversals
- Real-time dashboard with trade statistics
- Multi-timeframe scanner
- Advanced trade management with trailing stops
- Visual signals and trend highlighting

## System Requirements
- MetaTrader 5 (Build 3815 or higher)
- Windows 10/11 or compatible OS
- Minimum 4GB RAM
- Stable internet connection
- Hedging account type (recommended for multiple positions)

## Installation Steps

### 1. File Placement
Copy the following files to your MT5 data folder:

**Expert Advisor:**
- Copy `SupertrendEA.mq5` to `MQL5/Experts/` folder

**Custom Indicator:**
- Copy `Supertrend.mq5` to `MQL5/Indicators/` folder

**Settings File:**
- Copy `SupertrendEA.set` to `MQL5/Presets/` folder

### 2. Compilation
1. Open MetaEditor (F4 in MT5)
2. Navigate to `MQL5/Indicators/Supertrend.mq5`
3. Click Compile (F7) - should show "0 errors, 0 warnings"
4. Navigate to `MQL5/Experts/SupertrendEA.mq5`
5. Click Compile (F7) - should show "0 errors, 0 warnings"

### 3. EA Setup
1. In MT5, open the desired chart (EURUSD recommended for testing)
2. Go to Navigator → Expert Advisors
3. Drag `SupertrendEA` onto the chart
4. In the settings dialog:
   - Go to "Input Parameters" tab
   - Click "Load" and select `SupertrendEA.set`
   - Adjust settings as needed (see Configuration section)
   - Check "Allow live trading" and "Allow DLL imports"
5. Click OK

### 4. Verification
After installation, you should see:
- Dashboard panel in the top-left corner
- Scanner panel in the top-right corner
- Supertrend lines on the chart
- Buy/Sell arrows when signals occur

## Quick Start Configuration

### Conservative Settings (Low Risk)
```
Lot_Size = 0.01
Take_Profit = 300
Stop_Loss = 200
Enable_Trailing_Stop = true
Trailing_Stop_Distance = 150
ATR_Period = 14
ATR_Multiplier = 3.5
```

### Aggressive Settings (Higher Risk)
```
Lot_Size = 0.1
Take_Profit = 800
Stop_Loss = 400
Enable_Trailing_Stop = true
Trailing_Stop_Distance = 250
ATR_Period = 10
ATR_Multiplier = 2.5
```

### Scalping Settings (M1/M5 timeframes)
```
Lot_Size = 0.05
Take_Profit = 150
Stop_Loss = 100
Enable_Trailing_Stop = true
Trailing_Stop_Distance = 80
ATR_Period = 8
ATR_Multiplier = 2.0
Scanner_Timeframes = M1,M5,M15,M30
```

## Troubleshooting

### Common Issues

**EA not trading:**
- Check "Allow live trading" is enabled
- Verify account has sufficient margin
- Ensure market is open
- Check Enable_Auto_Trading parameter is true

**Dashboard/Scanner not showing:**
- Verify Show_Dashboard and Enable_Scanner are true
- Check X,Y position parameters (may be off-screen)
- Restart EA (remove and re-attach to chart)

**Compilation errors:**
- Ensure all files are in correct folders
- Check MT5 build version compatibility
- Verify no syntax errors in code

**Signals not appearing:**
- Check Show_Buy_Sell_Signals parameter
- Verify Supertrend calculation is working
- Ensure sufficient historical data

### Performance Optimization

**For VPS/Server:**
- Reduce Scanner_Update_Seconds to 10-15
- Disable chart visualization if not needed
- Use smaller Dashboard/Scanner sizes

**For Multiple Charts:**
- Use different Magic_Numbers for each chart
- Adjust Dashboard/Scanner positions to avoid overlap
- Consider disabling scanner on secondary charts

## Support and Updates

For technical support or updates:
1. Check the MT5 Journal tab for error messages
2. Verify all input parameters are within valid ranges
3. Test on demo account before live trading
4. Keep MT5 platform updated to latest version

## Important Notes

⚠️ **Risk Warning:** Trading involves substantial risk of loss. Test thoroughly on demo account before live trading.

⚠️ **Broker Compatibility:** Ensure your broker supports hedging if Allow_Hedging is enabled.

⚠️ **Timeframe Considerations:** EA works on all timeframes but performance varies. M15-H1 recommended for beginners.

⚠️ **News Events:** Consider disabling auto-trading during high-impact news releases.

## Next Steps
After successful installation, refer to the User Guide for detailed parameter explanations and trading strategies.
